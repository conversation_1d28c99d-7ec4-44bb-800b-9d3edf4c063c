import React, { useState, useEffect, useMemo } from "react";
import axios from "axios";
import { useAuth } from "../../context/NewAuthContext";
import * as XLSX from "xlsx";
import {
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { saveAs } from "file-saver";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
// import dayjs from 'dayjs';

// Helper function for fallback if dayjs is not available
function formatExpiryDate(date) {
  if (!date) return "N/A";
  const d = new Date(date);
  if (isNaN(d)) return "N/A";
  return `${d.getDate()}/${d.getMonth() + 1}/${d.getFullYear()}`;
}

const StockReport = () => {
  const { user } = useAuth();

  const [stockData, setStockData] = useState([]);
  const [productData, setProductData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // New state for batch tracking
  const [showBatchDetails, setShowBatchDetails] = useState(true);
  const [expandedRows, setExpandedRows] = useState(new Set());
  const [products, setProducts] = useState([]);

  const [fromDate, setFromDate] = useState("");
  const [toDate, setToDate] = useState("");
  const [lowStockThreshold] = useState(10);
  const [searchQuery, setSearchQuery] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("");
  const [supplierFilter, setSupplierFilter] = useState("");
  const [locationFilter, setLocationFilter] = useState("");
  const [storeLocationFilter, setStoreLocationFilter] = useState("");
  const [batchNumberFilter, setBatchNumberFilter] = useState("");

  const [categories, setCategories] = useState([]);

  // 1. State for storeLocations (from backend)
  const [storeLocations, setStoreLocations] = useState([]);

  const formatCurrency = (value) => {
    return new Intl.NumberFormat("en-LK", {
      style: "currency",
      currency: "LKR",
    }).format(value);
  };

  // Fetch products with variants (same as ItemWiseStockReport)
  const fetchProducts = async () => {
    try {
      const response = await axios.get("http://127.0.0.1:8000/api/products");
      const productsData = response.data.data || response.data;
      setProducts(productsData);
      console.log("Products with variants loaded:", productsData);
    } catch (error) {
      console.error("Error fetching products:", error);
    }
  };

  const fetchStockData = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await axios.get(
        "http://127.0.0.1:8000/api/detailed-stock-reports",
        {
          params: {
            fromDate,
            toDate,
            searchQuery,
            categoryFilter,
            supplierFilter,
            locationFilter,
          },
        }
      );
      console.log("Backend Response:", response.data);

      // Enhance stock data with batch information and transaction details
      const enhancedStockData = await enhanceWithBatchData(
        response.data,
        products
      );
      setStockData(Array.isArray(enhancedStockData) ? enhancedStockData : []);
    } catch (error) {
      console.error("Error fetching stock data:", error);
      const errorMessage =
        error.response?.data?.error ||
        "Failed to fetch stock data. Please try again later.";
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const fetchProductData = async () => {
    try {
      const response = await axios.get("http://127.0.0.1:8000/api/products");
      setProductData(
        Array.isArray(response.data.data) ? response.data.data : []
      );
    } catch (error) {
      console.error("Error fetching product data:", error);
    }
  };



  // Function to fetch batch transaction data (purchase, sales, and returns)
  const fetchBatchTransactions = async () => {
    try {
      // Fetch core transaction types (temporarily remove returns to restore functionality)
      const [salesResponse, invoicesResponse, purchasesResponse] =
        await Promise.all([
          axios.get("http://127.0.0.1:8000/api/sales"),
          axios.get("http://127.0.0.1:8000/api/invoices?per_page=1000"), // Get all invoices
          axios.get("http://127.0.0.1:8000/api/purchases"),
        ]);

      // TODO: Add returns back safely after core functionality is restored
      const salesReturnsResponse = { data: [] }; // Empty for now
      const purchaseReturnsResponse = { data: [] }; // Empty for now

      console.log("Sales response:", salesResponse.data);
      console.log("Invoices response:", invoicesResponse.data);
      console.log("Purchases response:", purchasesResponse.data);
      // Returns temporarily disabled for debugging
      // console.log("Sales returns response:", salesReturnsResponse.data);
      // console.log("Purchase returns response:", purchaseReturnsResponse.data);

      // Extract direct sales data
      const directSales = salesResponse.data || [];

      // Extract invoice data (handle paginated response)
      const invoiceData =
        invoicesResponse.data?.data || invoicesResponse.data || [];
      console.log("Extracted invoice data:", invoiceData);

      // Convert invoice data to match sales format for batch calculations
      const invoiceSales = invoiceData.map((invoice) => ({
        id: `invoice_${invoice.id}`,
        created_at: invoice.invoice_date || invoice.created_at,
        items: (invoice.items || []).map((item) => ({
          id: item.id,
          product_id: item.product_id,
          product_variant_id: item.product_variant_id, // Batch tracking
          batch_number: item.batch_number, // Batch tracking
          expiry_date: item.expiry_date, // Batch tracking
          quantity: item.quantity,
          unit_price: item.unit_price,
          sales_price: item.sales_price,
          discount: item.discount_amount || 0,
          special_discount: item.special_discount || 0,
        })),
      }));

      console.log("Converted invoice sales:", invoiceSales);

      // Temporarily disable returns processing to restore core functionality
      const salesReturns = []; // Empty array - no returns processing
      const purchaseReturns = []; // Empty array - no returns processing

      console.log("Sales returns:", salesReturns, "(temporarily disabled)");
      console.log(
        "Purchase returns:",
        purchaseReturns,
        "(temporarily disabled)"
      );

      // Combine all sales data (direct sales + invoice sales)
      const allSales = [...directSales, ...invoiceSales];
      console.log(
        "Combined sales count:",
        allSales.length,
        "Direct:",
        directSales.length,
        "Invoices:",
        invoiceSales.length
      );

      return {
        sales: allSales,
        purchases: purchasesResponse.data?.data || purchasesResponse.data || [], // Handle wrapped response
        salesReturns: salesReturns, // Add sales returns
        purchaseReturns: purchaseReturns, // Add purchase returns
      };
    } catch (error) {
      console.error("Error fetching batch transactions:", error);
      return {
        sales: [],
        purchases: [],
        salesReturns: [],
        purchaseReturns: [],
      };
    }
  };

  // Function to calculate batch-wise purchase and sold details using FIFO logic
  const calculateBatchMovements = (variant, transactions, allVariants) => {
    const { sales, purchases, salesReturns, purchaseReturns } = transactions;

    // Ensure sales and purchases are arrays with proper error handling
    const salesArray = Array.isArray(sales)
      ? sales
      : sales?.data
        ? sales.data
        : [];
    const purchasesArray = Array.isArray(purchases)
      ? purchases
      : purchases?.data
        ? purchases.data
        : [];

    console.log(
      "Processing variant:",
      variant.batch_number,
      "Product ID:",
      variant.product_id
    );

    try {
      // Calculate actual sales from this specific batch using database records
      const soldFromThisBatch = salesArray
        .filter((sale) => {
          return sale.items?.some(
            (item) =>
              // Match by product_variant_id (most accurate)
              (item.product_variant_id &&
                item.product_variant_id === variant.product_variant_id) ||
              // Match by batch_number (fallback)
              (item.batch_number &&
                item.batch_number === variant.batch_number) ||
              // Match by product_id only if no batch info available (legacy)
              (!item.product_variant_id &&
                !item.batch_number &&
                (item.product_id === variant.product_id ||
                  item.product_id?.toString() ===
                    variant.product_id?.toString()))
          );
        })
        .reduce((sum, sale) => {
          const matchingItems =
            sale.items?.filter(
              (item) =>
                // Match by product_variant_id (most accurate)
                (item.product_variant_id &&
                  item.product_variant_id === variant.product_variant_id) ||
                // Match by batch_number (fallback)
                (item.batch_number &&
                  item.batch_number === variant.batch_number) ||
                // Match by product_id only if no batch info available (legacy)
                (!item.product_variant_id &&
                  !item.batch_number &&
                  (item.product_id === variant.product_id ||
                    item.product_id?.toString() ===
                      variant.product_id?.toString()))
            ) || [];

          return (
            sum +
            matchingItems.reduce(
              (itemSum, item) => itemSum + parseFloat(item.quantity || 0),
              0
            )
          );
        }, 0);

      // Calculate purchases for this specific batch
      const purchasedForThisBatch = purchasesArray
        .filter((purchase) => {
          return purchase.items?.some(
            (item) =>
              // Match by product_variant_id (most accurate)
              (item.product_variant_id &&
                item.product_variant_id === variant.product_variant_id) ||
              // Match by batch_number (fallback)
              (item.batch_number &&
                item.batch_number === variant.batch_number) ||
              // Match by product_id ONLY if no batch info available (legacy)
              (!item.product_variant_id &&
                !item.batch_number &&
                (item.product_id === variant.product_id ||
                  item.product_id?.toString() ===
                    variant.product_id?.toString()))
          );
        })
        .reduce((sum, purchase) => {
          const matchingItems =
            purchase.items?.filter(
              (item) =>
                // Match by product_variant_id (most accurate)
                (item.product_variant_id &&
                  item.product_variant_id === variant.product_variant_id) ||
                // Match by batch_number (fallback)
                (item.batch_number &&
                  item.batch_number === variant.batch_number) ||
                // Match by product_id ONLY if no batch info available (legacy)
                (!item.product_variant_id &&
                  !item.batch_number &&
                  (item.product_id === variant.product_id ||
                    item.product_id?.toString() ===
                      variant.product_id?.toString()))
            ) || [];

          return (
            sum +
            matchingItems.reduce(
              (itemSum, item) => itemSum + parseFloat(item.quantity || 0),
              0
            )
          );
        }, 0);

      // Calculate sales returns for this specific batch (reduces sold quantity)
      // Safety check: only calculate if salesReturns data is available
      const salesReturnsArray = Array.isArray(salesReturns) ? salesReturns : [];
      const salesReturnedFromThisBatch =
        salesReturnsArray.length > 0
          ? salesReturnsArray
              .filter((salesReturn) => {
                return salesReturn.items?.some(
                  (item) =>
                    // Match by product_variant_id (most accurate)
                    (item.product_variant_id &&
                      item.product_variant_id === variant.product_variant_id) ||
                    // Match by batch_number (fallback)
                    (item.batch_number &&
                      item.batch_number === variant.batch_number) ||
                    // Match by product_id only if no batch info available (legacy)
                    (!item.product_variant_id &&
                      !item.batch_number &&
                      (item.product_id === variant.product_id ||
                        item.product_id?.toString() ===
                          variant.product_id?.toString()))
                );
              })
              .reduce((sum, salesReturn) => {
                const matchingItems =
                  salesReturn.items?.filter(
                    (item) =>
                      // Match by product_variant_id (most accurate)
                      (item.product_variant_id &&
                        item.product_variant_id ===
                          variant.product_variant_id) ||
                      // Match by batch_number (fallback)
                      (item.batch_number &&
                        item.batch_number === variant.batch_number) ||
                      // Match by product_id only if no batch info available (legacy)
                      (!item.product_variant_id &&
                        !item.batch_number &&
                        (item.product_id === variant.product_id ||
                          item.product_id?.toString() ===
                            variant.product_id?.toString()))
                  ) || [];

                return (
                  sum +
                  matchingItems.reduce(
                    (itemSum, item) => itemSum + parseFloat(item.quantity || 0),
                    0
                  )
                );
              }, 0)
          : 0; // Return 0 if no sales returns data

      // Calculate purchase returns for this specific batch (reduces purchased quantity)
      const purchaseReturnsArray = Array.isArray(purchaseReturns)
        ? purchaseReturns
        : [];
      const purchaseReturnedFromThisBatch =
        purchaseReturnsArray.length > 0
          ? purchaseReturnsArray
              .filter((purchaseReturn) => {
                return purchaseReturn.items?.some(
                  (item) =>
                    // Match by product_variant_id (most accurate)
                    (item.product_variant_id &&
                      item.product_variant_id === variant.product_variant_id) ||
                    // Match by batch_number (fallback)
                    (item.batch_number &&
                      item.batch_number === variant.batch_number) ||
                    // Match by product_id only if no batch info available (legacy)
                    (!item.product_variant_id &&
                      !item.batch_number &&
                      (item.product_id === variant.product_id ||
                        item.product_id?.toString() ===
                          variant.product_id?.toString()))
                );
              })
              .reduce((sum, purchaseReturn) => {
                const matchingItems =
                  purchaseReturn.items?.filter(
                    (item) =>
                      // Match by product_variant_id (most accurate)
                      (item.product_variant_id &&
                        item.product_variant_id ===
                          variant.product_variant_id) ||
                      // Match by batch_number (fallback)
                      (item.batch_number &&
                        item.batch_number === variant.batch_number) ||
                      // Match by product_id only if no batch info available (legacy)
                      (!item.product_variant_id &&
                        !item.batch_number &&
                        (item.product_id === variant.product_id ||
                          item.product_id?.toString() ===
                            variant.product_id?.toString()))
                  ) || [];

                return (
                  sum +
                  matchingItems.reduce(
                    (itemSum, item) => itemSum + parseFloat(item.quantity || 0),
                    0
                  )
                );
              }, 0)
          : 0; // Return 0 if no purchase returns data

      // Opening stock is FIXED - never changes, always use the stored value
      const openingStock = parseFloat(variant.opening_stock_quantity || 0);

      // Net purchased quantity = purchases - purchase returns
      const netPurchased =
        purchasedForThisBatch - purchaseReturnedFromThisBatch;

      // Net sold quantity = sales - sales returns
      const netSold = soldFromThisBatch - salesReturnedFromThisBatch;

      // FIXED: Use the stored closing_stock_quantity from the database
      // This value is updated by StockRecheckController when "update actual stock" is ticked
      let closingStock = parseFloat(variant.closing_stock_quantity || 0);
      
      // If no stored closing stock (legacy data), calculate it
      if (closingStock === 0 && variant.closing_stock_quantity === null) {
        closingStock = openingStock + netPurchased - netSold;
        console.log(`Batch ${variant.batch_number}: No stored closing stock, calculated: ${closingStock}`);
      } else {
        console.log(`Batch ${variant.batch_number}: Using stored closing stock: ${closingStock}`);
      }

      console.log(
        `Batch ${variant.batch_number}: Opening=${openingStock} (FIXED), Purchased=${purchasedForThisBatch}, PurchaseReturns=${purchaseReturnedFromThisBatch}, NetPurchased=${netPurchased}, Sold=${soldFromThisBatch}, SalesReturns=${salesReturnedFromThisBatch}, NetSold=${netSold}, Closing=${closingStock}`
      );

      // FIXED: Use gross values for display (like before) but net values for closing stock calculation
      // This ensures existing functionality works while adding returns support
      return {
        openingStock: openingStock, // FIXED - never changes
        purchased: purchasedForThisBatch, // Show gross purchases (like before)
        sold: soldFromThisBatch, // Show gross sales (like before)
        closingStock: closingStock, // FIXED: Use stored closing stock from database
        // Returns data for future use
        purchaseReturns: purchaseReturnedFromThisBatch, // Purchase returns
        salesReturns: salesReturnedFromThisBatch, // Sales returns
        netPurchased: netPurchased, // Net purchases (for internal calculations)
        netSold: netSold, // Net sales (for internal calculations)
        // Value calculations (use gross values for consistency)
        openingValue: openingStock * parseFloat(variant.buying_cost || 0), // FIXED
        purchaseValue:
          purchasedForThisBatch * parseFloat(variant.buying_cost || 0),
        salesValue: soldFromThisBatch * parseFloat(variant.sales_price || 0),
        closingValue: closingStock * parseFloat(variant.buying_cost || 0), // FIXED: Use stored closing stock
      };
    } catch (error) {
      console.error(
        "Error calculating batch movements for variant:",
        variant.batch_number,
        error
      );
      // Return fallback values
      const openingStock = parseFloat(variant.opening_stock_quantity || 0); // FIXED
      const purchased = 0; // No purchase data
      const sold = 0; // No sales data
      const closingStock = parseFloat(variant.closing_stock_quantity || openingStock); // Use stored closing stock or fallback to opening

      return {
        openingStock: openingStock, // FIXED - never changes
        purchased: purchased, // No purchases
        sold: sold, // No sales
        closingStock: closingStock, // FIXED: Use stored closing stock
        openingValue: openingStock * parseFloat(variant.buying_cost || 0), // FIXED
        purchaseValue: 0, // No purchases
        salesValue: 0, // No sales
        closingValue: closingStock * parseFloat(variant.buying_cost || 0), // FIXED: Use stored closing stock
      };
    }
  };

  // Function to enhance stock data with batch information and transaction details
  const enhanceWithBatchData = async (stockData, products) => {
    // Instead of recalculating, just attach batch info from backend (if present)
    return stockData.map((item) => {
      // If backend provides batches, use them directly
      if (item.hasBatches && Array.isArray(item.batches)) {
        // Use backend batch data as-is
        return {
          ...item,
          // Ensure these are the backend's values
          closingStock: item.closingStock,
          sold: item.sold,
          purchased: item.purchased,
          initialOpeningStock: item.initialOpeningStock,
          batches: item.batches.map((batch) => ({
            ...batch,
            closingStock: batch.closingStock,
            sold: batch.sold,
            purchased: batch.purchased,
            openingStock: batch.openingStock,
            // Use backend values for all batch fields
          })),
        };
      }
      // For non-batch items, use backend values
      return {
        ...item,
        closingStock: item.closingStock,
        sold: item.sold,
        purchased: item.purchased,
        initialOpeningStock: item.initialOpeningStock,
        batches: [],
      };
    });
  };

  // Function to toggle batch details for a product
  const toggleBatchDetails = (itemCode) => {
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(itemCode)) {
      newExpandedRows.delete(itemCode);
    } else {
      newExpandedRows.add(itemCode);
    }
    setExpandedRows(newExpandedRows);
  };

  // Initial data fetch (same as ItemWiseStockReport)
  useEffect(() => {
    fetchProducts();
    fetchProductData();
  }, []);

  // Fetch stock data when filters change or products are loaded
  useEffect(() => {
    if (products.length > 0) {
      fetchStockData();
    }
  }, [
    fromDate,
    toDate,
    searchQuery,
    categoryFilter,
    supplierFilter,
    locationFilter,
    products,
    showBatchDetails,
  ]);

  // Listen for stock recheck updates and refresh data
  useEffect(() => {
    const handler = () => {
      fetchStockData();
    };
    window.addEventListener('stockRecheckUpdated', handler);
    return () => window.removeEventListener('stockRecheckUpdated', handler);
  }, []);

  // 2. Fetch store locations from backend on mount
  useEffect(() => {
    const fetchStoreLocations = async () => {
      try {
        const response = await axios.get("http://127.0.0.1:8000/api/store-locations");
        setStoreLocations(response.data.map(loc => loc.store_name));
      } catch (error) {
        setStoreLocations([]);
        console.error("Error fetching store locations:", error);
      }
    };
    fetchStoreLocations();
  }, []);

  // 3. In calculateStockDetails, ensure storeLocationName matches backend
  const calculateStockDetails = (data) => {
    return data.map((item) => {
      const product = productData.find(
        (p) => p.item_code === item.itemCode || p.product_name === item.itemName
      );
      return {
        ...item,
        totalPurchaseValue:
          item.hasBatches && item.batches
            ? item.batches.reduce((sum, b) => sum + (b.closingStock ?? 0) * (b.buyingCost ?? 0), 0)
            : (item.closingStock ?? 0) * (item.costPrice ?? 0),
        totalSalesValue: item.closingStock * (item.sellingPrice ?? 0),
        rawLocation: product?.row ?? item.location?.identifier ?? "N/A",
        cabinetLocation: product?.cabinet ?? "N/A",
        storeLocationName: item.location?.store_name || product?.store_location || "N/A",
        batches: item.hasBatches ? item.batches : [],
        totalAvailableValue:
          item.hasBatches && item.batches
            ? item.batches.reduce((sum, b) => sum + (b.closingStock ?? 0) * (b.salesPrice ?? 0), 0)
            : (item.closingStock ?? 0) * (item.sellingPrice ?? 0),
      };
    });
  };
  const processedData = calculateStockDetails(stockData);

  // 4. Filter using storeLocationName and supplier
  const filteredData = processedData.filter((item) => {
    const matchesSearch =
      item.itemName?.toLowerCase().includes(searchQuery.toLowerCase()) ?? false;
    const matchesCategory = categoryFilter
      ? item.category === categoryFilter
      : true;
    const matchesLocation = locationFilter
      ? (item.location.type + " " + item.location.identifier)
          .toLowerCase()
          .includes(locationFilter.toLowerCase())
      : true;
    const matchesStoreLocation = storeLocationFilter
      ? item.storeLocationName === storeLocationFilter
      : true;
    const matchesSupplier = supplierFilter
      ? (item.supplier === supplierFilter)
      : true;
    // Batch number filter: match if any batch in item.batches matches
    const matchesBatchNumber = batchNumberFilter
      ? (Array.isArray(item.batches) && item.batches.some(batch =>
          batch.batchNumber && batch.batchNumber.toLowerCase().includes(batchNumberFilter.toLowerCase())
        ))
      : true;
    return matchesSearch && matchesCategory && matchesLocation && matchesStoreLocation && matchesSupplier && matchesBatchNumber;
  });

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await axios.get(
          "http://127.0.0.1:8000/api/categories"
        );
        if (Array.isArray(response.data)) {
          setCategories(response.data);
        } else {
          setCategories([]);
        }
      } catch (error) {
        console.error("Error fetching categories:", error);
        setCategories([]);
      }
    };
    fetchCategories();
  }, []);

  const totals = filteredData.reduce(
    (acc, item) => ({
      totalClosingStock: acc.totalClosingStock + (item.closingStock ?? 0),
      totalPurchaseValue:
        acc.totalPurchaseValue + (item.totalPurchaseValue ?? 0),
      totalSalesValue: acc.totalSalesValue + (item.totalSalesValue ?? 0),
      totalAvailableValue:
        acc.totalAvailableValue + (item.totalAvailableValue ?? 0),
      lowStockItems:
        (item.closingStock ?? 0) < lowStockThreshold
          ? acc.lowStockItems + 1
          : acc.lowStockItems,
      outOfStockItems:
        (item.closingStock ?? 0) === 0
          ? acc.outOfStockItems + 1
          : acc.outOfStockItems,
    }),
    {
      totalClosingStock: 0,
      totalPurchaseValue: 0,
      totalSalesValue: 0,
      totalAvailableValue: 0,
      lowStockItems: 0,
      outOfStockItems: 0,
    }
  );

  const topFastMovingProducts = [...filteredData]
    .sort((a, b) => (b.sold ?? 0) - (a.sold ?? 0))
    .slice(0, 5);

  const topSlowMovingProducts = [...filteredData]
    .filter((item) => (item.sold ?? 0) < 10)
    .sort((a, b) => (a.sold ?? 0) - (b.sold ?? 0))
    .slice(0, 5);

  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#AF19FF"];

  const exportToExcel = () => {
    const isPriceOrValue = (k) =>
      typeof k === "string" && /price|value/i.test(k);

    try {
      if (filteredData.length === 0) {
        alert("No data available to export.");
        return;
      }

      const workbook = XLSX.utils.book_new();
      const dateTime = new Date().toLocaleString();
      const fileName = `Stock_Report_${new Date().toISOString().slice(0, 10)}.xlsx`;

      const columns = [
        { key: "itemCode", name: "Item Code" },
        { key: "itemName", name: "Product Name" },
        { key: "category", name: "Category" },
        { key: "unit", name: "Unit" },
        { key: "initialOpeningStock", name: "Opening Stock" },
        { key: "purchased", name: "Purchased" },
        { key: "sold", name: "Sold" },
        { key: "closingStock", name: "Closing Stock" },
        { key: "totalPurchaseValue", name: "Total Value (Cost)" },
        { key: "totalAvailableValue", name: "Total Value (Selling)" },
        { key: "rawLocation", name: "Raw Location" },
        { key: "cabinetLocation", name: "Cabinet Location" },
        { key: "storeLocation", name: "Store Location" },
      ].filter((col) => col.key);

      console.table(columns.map((c) => ({ key: c.key, type: typeof c.key })));

      const worksheetData = filteredData.map((item, index) => {
        const row = { "No.": index + 1 };
        columns.forEach((col) => {
          if (!col?.key || typeof col.key !== "string") {
            return;
          }
          let value;

          if (col.key === "rawLocation") {
            value = item.rawLocation ?? "N/A";
          } else if (col.key === "cabinetLocation") {
            value = item.cabinetLocation ?? "N/A";
          } else if (col.key === "storeLocation") {
            value = item.storeLocation ?? "N/A";
          } else {
            value = item[col.key];
          }

          if (isPriceOrValue(col.key)) {
            value = value ?? 0;
            row[col.name] = value;
            row[`${col.name}_formatted`] = formatCurrency(value);
          } else if (typeof value === "number") {
            row[col.name] = value;
          } else {
            row[col.name] = value ?? "N/A";
          }
        });
        return row;
      });

      const totalsRow = { "No.": "Total" };
      columns.forEach((col) => {
        if (!col?.key || typeof col.key !== "string") return;

        if (
          col.key === "closingStock" ||
          col.key === "initialOpeningStock" ||
          col.key === "purchased" ||
          col.key === "sold"
        ) {
          totalsRow[col.name] = {
            f: `SUM(${XLSX.utils.encode_col(
              columns.findIndex((c) => c.key === col.key) + 1
            )}2:${XLSX.utils.encode_col(
              columns.findIndex((c) => c.key === col.key) + 1
            )}${filteredData.length + 1})`,
          };
        } else if (isPriceOrValue(col.key)) {
          totalsRow[col.name] = {
            f: `SUM(${XLSX.utils.encode_col(
              columns.findIndex((c) => c.key === col.key) + 1
            )}2:${XLSX.utils.encode_col(
              columns.findIndex((c) => c.key === col.key) + 1
            )}${filteredData.length + 1})`,
          };
          totalsRow[`${col.name}_formatted`] = {
            f: `TEXT(SUM(${XLSX.utils.encode_col(
              columns.findIndex((c) => c.key === col.key) + 1
            )}2:${XLSX.utils.encode_col(
              columns.findIndex((c) => c.key === col.key) + 1
            )}${filteredData.length + 1}), "[$LKR] #,##0.00")`,
          };
        } else {
          totalsRow[col.name] = "";
        }
      });

      const lowStockRow = { "No.": "Low Stock Items" };
      columns.forEach((col) => {
        if (col.key === "closingStock") {
          lowStockRow[col.name] = {
            f: `COUNTIF(${XLSX.utils.encode_col(
              columns.findIndex((c) => c.key === col.key) + 1
            )}2:${XLSX.utils.encode_col(
              columns.findIndex((c) => c.key === col.key) + 1
            )}${filteredData.length + 1},"<"&${lowStockThreshold})`,
          };
        } else if (col.key === "itemName") {
          lowStockRow[col.name] = "Items with stock < " + lowStockThreshold;
        } else {
          lowStockRow[col.name] = "";
        }
      });

      const outOfStockRow = { "No.": "Out of Stock Items" };
      columns.forEach((col) => {
        if (col.key === "closingStock") {
          outOfStockRow[col.name] = {
            f: `COUNTIF(${XLSX.utils.encode_col(
              columns.findIndex((c) => c.key === col.key) + 1
            )}2:${XLSX.utils.encode_col(
              columns.findIndex((c) => c.key === col.key) + 1
            )}${filteredData.length + 1},"=0")`,
          };
        } else if (col.key === "itemName") {
          outOfStockRow[col.name] = "Items with zero stock";
        } else {
          outOfStockRow[col.name] = "";
        }
      });

      const exportData = [
        ...worksheetData,
        {},
        totalsRow,
        lowStockRow,
        outOfStockRow,
      ];

      const worksheet = XLSX.utils.json_to_sheet(exportData, {
        header: [
          "No.",
          ...columns.map((col) => col.name),
          ...columns
            .filter(
              (col) =>
                typeof col.key === "string" &&
                (col.key.toLowerCase().includes("price") ||
                  col.key.toLowerCase().includes("value"))
            )
            .map((col) => `${col.name}`),
        ],
        skipHeader: false,
      });

      const header = ["No.", ...columns.map((col) => col.name)];
      XLSX.utils.sheet_add_aoa(worksheet, [header], { origin: "A1" });

      const colWidths = header.map((h) => ({
        wch:
          Math.max(
            h.length,
            ...worksheetData.map((row) => String(row[h] || "").length)
          ) + 2,
      }));
      worksheet["!cols"] = colWidths;

      const range = XLSX.utils.decode_range(worksheet["!ref"]);

      for (let C = range.s.c; C <= range.e.c; ++C) {
        const cellAddress = XLSX.utils.encode_cell({ r: 0, c: C });
        if (!worksheet[cellAddress]) continue;
        worksheet[cellAddress].s = {
          font: {
            bold: true,
            color: { rgb: "FFFFFF" },
            sz: 12,
          },
          fill: {
            fgColor: { rgb: "4F81BD" },
            patternType: "solid",
          },
          border: {
            top: { style: "thin", color: { rgb: "000000" } },
            bottom: { style: "thin", color: { rgb: "000000" } },
            left: { style: "thin", color: { rgb: "000000" } },
            right: { style: "thin", color: { rgb: "000000" } },
          },
          alignment: {
            horizontal: "center",
            vertical: "center",
            wrapText: true,
          },
        };
      }

      for (let R = range.s.r + 1; R <= range.e.r; ++R) {
        for (let C = range.s.c; C <= range.e.c; ++C) {
          const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
          if (!worksheet[cellAddress]) continue;

          if (!worksheet[cellAddress].s) worksheet[cellAddress].s = {};

          worksheet[cellAddress].s.border = {
            top: { style: "thin", color: { rgb: "DDDDDD" } },
            bottom: { style: "thin", color: { rgb: "DDDDDD" } },
            left: { style: "thin", color: { rgb: "DDDDDD" } },
            right: { style: "thin", color: { rgb: "DDDDDD" } },
          };

          const headerName = header[C];
          if (typeof headerName !== "string") {
            continue;
          }
          if (
            headerName.toLowerCase().includes("price") ||
            headerName.toLowerCase().includes("value") ||
            headerName.toLowerCase().includes("stock") ||
            headerName.toLowerCase().includes("sold") ||
            headerName.toLowerCase().includes("purchased")
          ) {
            worksheet[cellAddress].s.alignment = {
              horizontal: "right",
              vertical: "center",
            };
            if (
              headerName.toLowerCase().includes("price") ||
              headerName.toLowerCase().includes("value")
            ) {
              worksheet[cellAddress].s.numFmt = '"LKR"#,##0.00';
            }
          } else {
            worksheet[cellAddress].s.alignment = {
              horizontal: "left",
              vertical: "center",
            };
          }

          const dataRowIndex = R - 1;
          if (
            dataRowIndex < worksheetData.length &&
            worksheetData[dataRowIndex]?.closingStock < lowStockThreshold
          ) {
            worksheet[cellAddress].s.fill = {
              fgColor: { rgb: "FFCCCC" },
              patternType: "solid",
            };
          }

          if (
            R === worksheetData.length + 1 ||
            R === worksheetData.length + 3 ||
            R === worksheetData.length + 4
          ) {
            worksheet[cellAddress].s.font = {
              bold: true,
              color:
                R === worksheetData.length + 1
                  ? { rgb: "000000" }
                  : { rgb: "FF0000" },
            };
            worksheet[cellAddress].s.fill = {
              fgColor: {
                rgb: R === worksheetData.length + 1 ? "F2F2F2" : "FFF2F2",
              },
              patternType: "solid",
            };
          }
        }
      }

      XLSX.utils.book_append_sheet(workbook, worksheet, "Stock Report");

      const reportDetails = [
        ["STOCK REPORT", "", "", ""],
        ["", "", "", ""],
        [
          "Company Name:",
          "[Your Company Name]",
          "Report Period:",
          `${fromDate} to ${toDate}`,
        ],
        [
          "Generated By:",
          user?.name || "Unknown User",
          "Date & Time:",
          dateTime,
        ],
        [
          "Authorized By:",
          "[Manager Name]",
          "Low Stock Threshold:",
          lowStockThreshold,
        ],
        ["Company Contact:", "[Your Business Contact Info]", "", ""],
        ["", "", "", ""],
        ["SUMMARY STATISTICS", "", "", ""],
        [
          "Total Products:",
          filteredData.length,
          "Total Closing Stock:",
          {
            f: `'Stock Report'!${XLSX.utils.encode_col(columns.findIndex((c) => c.key === "closingStock") + 1)}${worksheetData.length + 2}`,
          },
        ],
        [
          "Total Cost Value:",
          {
            f: `'Stock Report'!${XLSX.utils.encode_col(columns.findIndex((c) => c.key === "totalPurchaseValue") + 1)}${worksheetData.length + 2}`,
          },
          "Total Selling Value:",
          {
            f: `'Stock Report'!${XLSX.utils.encode_col(columns.findIndex((c) => c.key === "totalAvailableValue") + 1)}${worksheetData.length + 2}`,
          },
        ],
        [
          "Low Stock Items:",
          {
            f: `'Stock Report'!${XLSX.utils.encode_col(columns.findIndex((c) => c.key === "closingStock") + 1)}${worksheetData.length + 4}`,
          },
          "Out of Stock Items:",
          {
            f: `'Stock Report'!${XLSX.utils.encode_col(columns.findIndex((c) => c.key === "closingStock") + 1)}${worksheetData.length + 5}`,
          },
        ],
        ["", "", "", ""],
        ["NOTES:", "", "", ""],
        ["", "1. This report is system generated", "", ""],
        ["", "2. For any discrepancies, please contact support", "", ""],
        ["", "3. Low stock threshold is set to " + lowStockThreshold, "", ""],
      ];

      const detailsSheet = XLSX.utils.aoa_to_sheet(reportDetails);

      detailsSheet["!merges"] = [
        { s: { r: 0, c: 0 }, e: { r: 0, c: 3 } },
        { s: { r: 7, c: 0 }, e: { r: 7, c: 3 } },
        { s: { r: 12, c: 0 }, e: { r: 12, c: 3 } },
        { s: { r: 13, c: 1 }, e: { r: 13, c: 3 } },
        { s: { r: 14, c: 1 }, e: { r: 14, c: 3 } },
        { s: { r: 15, c: 1 }, e: { r: 15, c: 3 } },
      ];

      const detailsRange = XLSX.utils.decode_range(detailsSheet["!ref"]);
      for (let R = detailsRange.s.r; R <= detailsRange.e.r; ++R) {
        for (let C = detailsRange.s.c; C <= detailsRange.e.c; ++C) {
          const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
          if (!detailsSheet[cellAddress]) continue;

          detailsSheet[cellAddress].s = {
            font: {
              bold:
                R === 0 || R === 7 || R === 12 || (R >= 2 && R <= 4)
                  ? true
                  : false,
              color: R === 0 ? { rgb: "4F81BD" } : { rgb: "000000" },
            },
            alignment: {
              vertical: "center",
              horizontal: R === 0 || R === 7 || R === 12 ? "center" : "left",
            },
          };

          if (R === 0) {
            detailsSheet[cellAddress].s.font = {
              bold: true,
              sz: 16,
              color: { rgb: "4F81BD" },
            };
          }

          if (R === 7) {
            detailsSheet[cellAddress].s.font = {
              bold: true,
              sz: 14,
              color: { rgb: "4F81BD" },
            };
            detailsSheet[cellAddress].s.fill = {
              fgColor: { rgb: "F2F2F2" },
              patternType: "solid",
            };
          }

          if (
            (R === 8 && C === 3) ||
            (R === 9 && (C === 1 || C === 3)) ||
            (R === 10 && (C === 1 || C === 3))
          ) {
            detailsSheet[cellAddress].s.numFmt =
              C === 3 && R === 8 ? "0" : '"LKR"#,##0.00';
          }
        }
      }

      detailsSheet["!cols"] = [
        { wch: 20 },
        { wch: 30 },
        { wch: 20 },
        { wch: 30 },
      ];

      XLSX.utils.book_append_sheet(workbook, detailsSheet, "Report Details");

      XLSX.writeFile(workbook, fileName);
    } catch (error) {
      console.error("Error exporting to Excel:", error);
      alert("An error occurred while exporting to Excel. Please try again.");
    }
  };

  const printReport = () => {
    try {
      const input = document.getElementById("stock-report");

      if (!input) {
        alert("Report content not found. Cannot print.");
        return;
      }

      const printWindow = window.open("", "_blank");
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>Stock Report</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; color: #333; }
            h1 { color: #4F81BD; text-align: center; margin-bottom: 30px; }
            table { width: 100%; border-collapse: collapse; margin-bottom: 30px; }
            th { background-color: #4F81BD; color: white; padding: 10px; text-align: left; }
            td { padding: 8px; border: 1px solid #ddd; }
            .total-row { font-weight: bold; background-color: #f2f2f2; }
            .report-meta { margin-bottom: 30px; }
            .meta-row { display: flex; margin-bottom: 8px; }
            .meta-label { font-weight: bold; min-width: 150px; }
            .notes { margin-top: 30px; }
            @media print {
              body { padding: 0; }
              .no-print { display: none; }
              @page { size: A4 landscape; margin: 10mm; }
              table { page-break-inside: avoid; }
              tr { page-break-inside: avoid; page-break-after: auto; }
            }
            @page { size: A4 landscape; margin: 10mm; }
          </style>
        </head>
        <body>
          <h1>STOCK REPORT</h1>
          
          <div class="report-meta">
            <div class="meta-row">
              <div class="meta-label">Report Period:</div>
              <div>${fromDate} to ${toDate}</div>
            </div>
            <div class="meta-row">
              <div class="meta-label">Generated On:</div>
              <div>${new Date().toLocaleString()}</div>
            </div>
            <div class="meta-row">
              <div class="meta-label">Generated By:</div>
              <div>${user?.name || "Unknown User"}</div>
            </div>
            <div class="meta-row">
              <div class="meta-label">Authorized By:</div>
              <div>[Manager Name]</div>
            </div>
          </div>
          
          <table>
            <thead>
              <tr>
                <th>No.</th>
                <th>Product Name</th>
                <th>Category</th>
                <th>Opening Stock</th>
                <th>Purchased</th>
                <th>Sold</th>
                <th>Closing Stock</th>
                <th>Unit</th>
                <th>Total Value (Cost)</th>
                <th>Total Value (Selling)</th>
                <th>Location</th>
              </tr>
            </thead>
            <tbody>
              ${processedData
                .map(
                  (item, index) => `
                <tr>
                  <td>${index + 1}</td>
                  <td>${item.itemName ?? "N/A"}</td>
                  <td>${item.category ?? "N/A"}</td>
                  <td>${item.initialOpeningStock ?? 0}</td>
                  <td>${item.purchased ?? 0}</td>
                  <td>${item.sold ?? 0}</td>
                  <td>${item.closingStock ?? 0}</td>
                  <td>${item.unit ?? "N/A"}</td>
                  <td>${item.totalPurchaseValue?.toFixed(2) ?? "0.00"}</td>
                  <td>${item.totalAvailableValue?.toFixed(2) ?? "0.00"}</td>
                  <td>${item.storeLocationName}</td>
                </tr>
              `
                )
                .join("")}
            </tbody>
          </table>
          
          <div class="notes">
            <p><strong>Notes:</strong></p>
            <ol>
              <li>This report is system generated</li>
              <li>For any discrepancies, please contact support</li>
            </ol>
          </div>
          
          <div class="no-print" style="margin-top: 20px;">
            <button onclick="window.print()">Print Report</button>
            <button onclick="window.close()">Close</button>
          </div>
          
          <script>
            window.onload = function() {
              setTimeout(function() {
                window.print();
              }, 500);
            };
            
            window.onafterprint = function() {
              setTimeout(function() {
                window.close();
              }, 500);
            };
          </script>
        </body>
        </html>
      `);

      printWindow.document.close();
    } catch (error) {
      console.error("Error printing report:", error);
      alert("An error occurred while printing. Please try again.");
    }
  };

  const handleRecalculateClosingStock = async () => {
    setLoading(true);
    try {
      const response = await axios.post('http://127.0.0.1:8000/api/recalculate-closing-stock');
      
      if (response.data.message) {
        alert(`Closing stock recalculated successfully!\nUpdated ${response.data.variants_updated} variants and ${response.data.products_updated} products.`);
        // Refresh the stock data
        fetchStockData();
      }
    } catch (error) {
      console.error('Error recalculating closing stock:', error);
      alert('Error recalculating closing stock. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const uniqueStoreLocations = Array.from(
    new Set(processedData.map(item => item.storeLocationName).filter(loc => loc && loc !== "N/A"))
  );

  // Extract unique suppliers from products for the filter dropdown
  const uniqueSuppliers = useMemo(() => {
    const set = new Set();
    products.forEach((p) => {
      if (p.supplier && p.supplier.trim() !== "") set.add(p.supplier.trim());
    });
    return Array.from(set);
  }, [products]);

  useEffect(() => {
    if (stockData.length > 0) {
      console.log("DEBUG: First 5 stockData items:", stockData.slice(0, 5));
    }
  }, [stockData]);

  return (
    <div
      className="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen flex flex-col items-center font-sans"
      id="stock-report"
    >
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white w-full text-center py-4 rounded-lg shadow-lg mb-6">
        <h1 className="text-2xl font-bold">Stock Report</h1>
        <div className="justify-end gap-4 mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
          <p className="text-sm mt-1">
            Generated By: {user?.name || "Unknown User"}
          </p>
          <p className="text-sm mt-1">
            Date & Time: {new Date().toLocaleString()}
          </p>
        </div>
      </div>

      {error && (
        <div className="w-full max-w-6xl mb-4 p-4 bg-red-100 text-red-800 rounded-lg shadow-md flex items-center justify-between">
          <span>{error}</span>
          <button
            onClick={fetchStockData}
            className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 transition"
          >
            Retry
          </button>
        </div>
      )}

      <div className="w-full max-w-6xl mb-2">
        <div className="grid grid-cols-1 lg:grid-cols-6 gap-4 mb-4"> {/* changed to 6 columns */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-200">
              Search
            </label>
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full p-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
              placeholder="Search by product name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-200">
              Category
            </label>
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="w-full p-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
            >
              <option value="">All Categories</option>
              {categories.map((category, index) => (
                <option key={index} value={category.name}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>
          {/* Supplier Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-200">
              Supplier
            </label>
            <select
              value={supplierFilter}
              onChange={e => setSupplierFilter(e.target.value)}
              className="w-full p-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
            >
              <option value="">All Suppliers</option>
              {uniqueSuppliers.map((supplier, idx) => (
                <option key={idx} value={supplier}>{supplier}</option>
              ))}
            </select>
          </div>
          {/* Store Location Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-200">
              Store Location
            </label>
            <select
              value={storeLocationFilter}
              onChange={e => setStoreLocationFilter(e.target.value)}
              className="w-full p-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
            >
              <option value="">All Locations</option>
              {storeLocations.map((loc, idx) => (
                <option key={idx} value={loc}>{loc}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-200">
              Batch Number
            </label>
            <input
              type="text"
              value={batchNumberFilter}
              onChange={e => setBatchNumberFilter(e.target.value)}
              className="w-full p-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
              placeholder="Filter by batch number"
            />
          </div>
        </div>
      </div>

      <div className="w-full max-w-6xl mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold dark:text-gray-200">Summary</h2>
          <div className="flex space-x-2">
            <button
              onClick={handleRecalculateClosingStock}
              disabled={loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading ? 'Recalculating...' : 'Recalculate Closing Stock'}
            </button>
            {/* <button
              onClick={() => window.location.href = '/stock-recheck'}
              className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
            >
              Stock Recheck
            </button> */}
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-cyan-800 p-4 rounded-lg">
            <p className="text-sm text-cyan-500">Total Products in Stock</p>
            <p className="text-2xl text-cyan-300 font-bold">
              {filteredData.length}
            </p>
          </div>
          <div className="bg-rose-800 p-4 rounded-lg">
            <p className="text-sm text-pink-500">Total Stock Quantity</p>
            <p className="text-2xl text-pink-300 font-bold">
              {totals.totalClosingStock}
            </p>
          </div>
          <div className="bg-lime-800 p-4 rounded-lg">
            <p className="text-sm text-lime-500">
              Total Stock Value (Cost Price)
            </p>
            <p className="text-2xl text-lime-300 font-bold">
              {formatCurrency(totals.totalPurchaseValue)}
            </p>
          </div>
          <div className="bg-purple-800 p-4 rounded-lg">
            <p className="text-sm text-purple-500">
              Total Stock Value (Selling Price)
            </p>
            <p className="text-2xl text-purple-300 font-bold">
              {formatCurrency(totals.totalAvailableValue)}
            </p>
          </div>
          <div className="bg-orange-800 p-4 rounded-lg">
            <p className="text-sm text-orange-500">Out-of-Stock Items</p>
            <p className="text-2xl text-orange-300 font-bold">
              {totals.outOfStockItems}
            </p>
          </div>
          <div className="bg-yellow-800 p-4 rounded-lg">
            <p className="text-sm text-yellow-500">Low Stock Items</p>
            <p className="text-2xl text-yellow-300 font-bold">
              {totals.lowStockItems}
            </p>
          </div>
        </div>
      </div>

      <div className="w-full max-w-6xl mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold dark:text-gray-200">
            Detailed Stock Table
          </h2>
          <button
            onClick={() => setShowBatchDetails(!showBatchDetails)}
            className={`px-4 py-2 rounded-lg font-medium transition duration-300 ${
              showBatchDetails
                ? "bg-blue-600 text-white hover:bg-blue-700"
                : "bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
            }`}
          >
            {showBatchDetails ? "Hide Batch Details" : "Show Batch Details"}
          </button>
        </div>
        <div
          className="overflow-x-auto rounded-lg shadow-lg"
          style={{ maxHeight: "500px", overflowY: "auto" }}
        >
          {loading ? (
            <div className="flex justify-center items-center p-8">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : filteredData.length === 0 ? (
            <div className="p-4 text-center text-gray-500 dark:text-gray-400">
              No stock data available for the selected filters.
            </div>
          ) : (
            <table className="w-full border-collapse bg-white dark:bg-gray-800">
              <thead className="bg-blue-100 dark:bg-blue-800 sticky top-0">
                <tr>
                  <th className="border-b-2 border-blue-200 px-4 py-3 text-left text-sm font-semibold text-blue-800 dark:text-blue-200">
                    No.
                  </th>
                  <th className="border-b-2 border-blue-200 px-4 py-3 text-left text-sm font-semibold text-blue-800 dark:text-blue-200">
                    Product Name
                  </th>
                  <th className="border-b-2 border-blue-200 px-4 py-3 text-left text-sm font-semibold text-blue-800 dark:text-blue-200">
                    Category
                  </th>
                  <th className="border-b-2 border-blue-200 px-4 py-3 text-left text-sm font-semibold text-blue-800 dark:text-blue-200">
                    Supplier
                  </th>
                  {showBatchDetails && (
                    <th className="border-b-2 border-blue-200 px-4 py-3 text-left text-sm font-semibold text-blue-800 dark:text-blue-200">
                      Batch Details
                    </th>
                  )}
                  <th className="border-b-2 border-blue-200 px-4 py-3 text-left text-sm font-semibold text-blue-800 dark:text-blue-200">
                    Opening Stock
                  </th>
                  <th className="border-b-2 border-blue-200 px-4 py-3 text-left text-sm font-semibold text-blue-800 dark:text-blue-200">
                    Purchased
                  </th>
                  <th className="border-b-2 border-blue-200 px-4 py-3 text-left text-sm font-semibold text-blue-800 dark:text-blue-200">
                    Sold
                  </th>
                  <th className="border-b-2 border-blue-200 px-4 py-3 text-left text-sm font-semibold text-blue-800 dark:text-blue-200">
                    Closing Stock
                  </th>
                  <th className="border-b-2 border-blue-200 px-4 py-3 text-left text-sm font-semibold text-blue-800 dark:text-blue-200">
                    Unit
                  </th>
                  <th className="border-b-2 border-blue-200 px-4 py-3 text-left text-sm font-semibold text-blue-800 dark:text-blue-200">
                    Total Value (Cost)
                  </th>
                  <th className="border-b-2 border-blue-200 px-4 py-3 text-left text-sm font-semibold text-blue-800 dark:text-blue-200">
                    Total Value (Selling)
                  </th>
                  <th className="border-b-2 border-blue-200 px-4 py-3 text-left text-sm font-semibold text-blue-800 dark:text-blue-200">
                    Location
                  </th>
                </tr>
              </thead>
              <tbody>
                {filteredData.map((item, index) => (
                  <React.Fragment key={index}>
                    <tr
                      className={`hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-200 ${
                        (item.closingStock ?? 0) < lowStockThreshold
                          ? "bg-red-100 dark:bg-red-800 animate-pulse"
                          : ""
                      }`}
                    >
                      <td className="border-b border-gray-100 dark:border-gray-700 px-4 py-3 text-sm text-gray-700 dark:text-gray-200">
                        {index + 1}
                      </td>
                      <td className="border-b border-gray-100 dark:border-gray-700 px-4 py-3 text-sm text-gray-700 dark:text-gray-200">
                        <div className="flex items-center">
                          {item.itemName ?? "N/A"}
                          {showBatchDetails && item.hasBatches && (
                            <button
                              onClick={() => toggleBatchDetails(item.itemCode)}
                              className="ml-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                            >
                              {expandedRows.has(item.itemCode) ? "▼" : "▶"}
                            </button>
                          )}
                        </div>
                      </td>
                      <td className="border-b border-gray-100 dark:border-gray-700 px-4 py-3 text-sm text-gray-700 dark:text-gray-200">
                        {item.category ?? "N/A"}
                      </td>
                      <td className="border-b border-gray-100 dark:border-gray-700 px-4 py-3 text-sm text-gray-700 dark:text-gray-200">
                        {item.supplier ?? "N/A"}
                      </td>
                      {showBatchDetails && (
                        <td className="border-b border-gray-100 dark:border-gray-700 px-4 py-3 text-sm text-gray-700 dark:text-gray-200">
                          {item.hasBatches ? (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                              {item.batches.length} Batch
                              {item.batches.length !== 1 ? "es" : ""}
                            </span>
                          ) : (
                            "No Batches"
                          )}
                        </td>
                      )}
                      <td className="border-b border-gray-100 dark:border-gray-700 px-4 py-3 text-sm text-gray-700 dark:text-gray-200">
                        {item.initialOpeningStock ?? item.openingStock ?? 0}
                      </td>
                      <td className="border-b border-gray-100 dark:border-gray-700 px-4 py-3 text-sm text-gray-700 dark:text-gray-200">
                        {item.purchased ?? 0}
                      </td>
                      <td className="border-b border-gray-100 dark:border-gray-700 px-4 py-3 text-sm text-gray-700 dark:text-gray-200">
                        {item.sold ?? 0}
                      </td>
                      <td className="border-b border-gray-100 dark:border-gray-700 px-4 py-3 text-sm text-gray-700 dark:text-gray-200">
                        {item.closingStock ?? 0}
                      </td>
                      <td className="border-b border-gray-100 dark:border-gray-700 px-4 py-3 text-sm text-gray-700 dark:text-gray-200">
                        {item.unit ?? "N/A"}
                      </td>
                      <td className="border-b border-gray-100 dark:border-gray-700 px-4 py-3 text-sm text-gray-700 dark:text-gray-200">
                        {formatCurrency(item.totalPurchaseValue ?? 0)}
                      </td>
                      <td className="border-b border-gray-100 dark:border-gray-700 px-4 py-3 text-sm text-gray-700 dark:text-gray-200">
                        {formatCurrency(item.totalAvailableValue ?? 0)}
                      </td>
                      <td className="border-b border-gray-100 dark:border-gray-700 px-4 py-3 text-sm text-gray-700 dark:text-gray-200">
                        {item.storeLocationName}
                      </td>
                    </tr>
                    {showBatchDetails &&
                      item.hasBatches &&
                      expandedRows.has(item.itemCode) && (
                        <tr>
                          <td
                            colSpan={14}
                            className="border-b border-gray-100 dark:border-gray-700 px-4 py-3 bg-gray-50 dark:bg-gray-700"
                          >
                            <div className="p-4">
                              <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-200 mb-2">
                                Batch Details for {item.itemName}
                              </h3>
                              <table className="w-full border-collapse">
                                <thead>
                                  <tr className="bg-gray-200 dark:bg-gray-600">
                                    <th className="px-4 py-2 text-left text-xs font-semibold text-gray-700 dark:text-gray-200">
                                      Batch Number
                                    </th>
                                    <th className="px-4 py-2 text-left text-xs font-semibold text-gray-700 dark:text-gray-200">
                                      cost price
                                    </th>
                                    <th className="px-4 py-2 text-left text-xs font-semibold text-gray-700 dark:text-gray-200">
                                      sales price
                                    </th>
                                    
                                    <th className="px-4 py-2 text-left text-xs font-semibold text-gray-700 dark:text-gray-200">
                                      Opening Stock
                                    </th>
                                    <th className="px-4 py-2 text-left text-xs font-semibold text-gray-700 dark:text-gray-200">
                                      Purchased
                                    </th>
                                    <th className="px-4 py-2 text-left text-xs font-semibold text-gray-700 dark:text-gray-200">
                                      Sold
                                    </th>
                                    <th className="px-4 py-2 text-left text-xs font-semibold text-gray-700 dark:text-gray-200">
                                      Closing Stock
                                    </th>
                                    <th className="px-4 py-2 text-left text-xs font-semibold text-gray-700 dark:text-gray-200">
                                      Expiry Date
                                    </th>
                                    <th className="px-4 py-2 text-left text-xs font-semibold text-gray-700 dark:text-gray-200">
                                      Turnover Rate
                                    </th>
                                    <th className="px-4 py-2 text-left text-xs font-semibold text-gray-700 dark:text-gray-200">
                                      Opening Value
                                    </th>
                                    <th className="px-4 py-2 text-left text-xs font-semibold text-gray-700 dark:text-gray-200">
                                      Purchase Value
                                    </th>
                                    <th className="px-4 py-2 text-left text-xs font-semibold text-gray-700 dark:text-gray-200">
                                      Sales Value
                                    </th>
                                    <th className="px-4 py-2 text-left text-xs font-semibold text-gray-700 dark:text-gray-200">
                                      Closing Value
                                    </th>
                                    <th className="px-4 py-2 text-left text-xs font-semibold text-gray-700 dark:text-gray-200">
                                      Location
                                    </th>
                                    {/* <th className="px-4 py-2 text-left text-xs font-semibold text-gray-700 dark:text-gray-200">Cost Price</th>
                                    <th className="px-4 py-2 text-left text-xs font-semibold text-gray-700 dark:text-gray-200">Selling Price</th> */}
                                  </tr>
                                </thead>
                                <tbody>
                                  {item.batches.map((batch, batchIndex) => {
                                    const avgStock = ((batch.openingStock ?? 0) + (batch.closingStock ?? 0)) / 2;
                                    const turnoverRate = avgStock > 0 ? ((batch.sold ?? 0) / avgStock) * 100 : 0;
                                    return (
                                      <tr key={batchIndex} className="hover:bg-gray-100 dark:hover:bg-gray-600">
                                        <td className="px-4 py-2 text-sm text-gray-700 dark:text-gray-200">{batch.batchNumber ?? "N/A"}</td>
                                        <td className="px-4 py-2 text-sm font-bold">{formatCurrency(batch.buyingCost ?? 0)}</td>
                                        <td className="px-4 py-2 text-sm font-bold">{formatCurrency(batch.salesPrice ?? 0)}</td>
                                        <td className="px-4 py-2 text-sm text-orange-600 font-bold">{batch.openingStock ?? 0}</td>
                                        <td className="px-4 py-2 text-sm text-blue-600 font-bold">{batch.purchased ?? 0}</td>
                                        <td className="px-4 py-2 text-sm text-green-600 font-bold">{batch.sold ?? 0}</td>
                                        <td className="px-4 py-2 text-sm font-bold">{batch.closingStock ?? 0}</td>
                                        <td className="px-4 py-2 text-sm text-gray-700 dark:text-gray-200">{formatExpiryDate(batch.expiryDate)}</td>
                                        <td className="px-4 py-2 text-sm text-purple-700 dark:text-purple-300 font-bold">{Number.isFinite(turnoverRate) ? turnoverRate.toFixed(1) : "0.0"}%</td>
                                        <td className="px-4 py-2 text-sm font-bold">{formatCurrency((batch.openingStock ?? 0) * (batch.buyingCost ?? 0))}</td>
                                        <td className="px-4 py-2 text-sm font-bold">{formatCurrency((batch.purchased ?? 0) * (batch.buyingCost ?? 0))}</td>
                                        <td className="px-4 py-2 text-sm font-bold">{formatCurrency((batch.sold ?? 0) * (batch.salesPrice ?? 0))}</td>
                                        <td className="px-4 py-2 text-sm font-bold">{formatCurrency((batch.closingStock ?? 0) * (batch.buyingCost ?? 0))}</td>
                                        <td className="px-4 py-2 text-sm text-gray-700 dark:text-gray-200">{batch.storeLocation ?? "N/A"}</td>
                                        {/* <td className="px-4 py-2 text-sm font-bold">{formatCurrency(batch.buyingCost ?? 0)}</td>
                                        <td className="px-4 py-2 text-sm font-bold">{formatCurrency(batch.salesPrice ?? 0)}</td> */}
                                      </tr>
                                    );
                                  })}
                                  {/* Totals row for batch details */}
                                  <tr className="font-bold bg-gray-100 dark:bg-gray-800">
                                    <td className="px-4 py-2 text-sm text-gray-700 dark:text-gray-200">
                                      TOTAL ({item.batches.length} Batch{item.batches.length !== 1 ? "es" : ""})
                                    </td>
                                    <td className="px-4 py-2 text-sm text-gray-700 dark:text-gray-200"></td>
                                    <td className="px-4 py-2 text-sm text-gray-700 dark:text-gray-200"></td>
                                    <td className="px-4 py-2 text-sm text-orange-600 font-bold">
                                      {item.batches.reduce((sum, b) => sum + (b.openingStock ?? 0), 0)}
                                    </td>
                                    {/* <td className="px-4 py-2 text-sm text-blue-600 font-bold">
                                      {item.batches.reduce((sum, b) => sum + (b.purchased ?? 0), 0)}
                                    </td> */}
                                    <td className="px-4 py-2 text-sm text-blue-600 font-bold">{item.batches.reduce((sum, b) => sum + Number(b.purchased ?? 0), 0)}</td>
                                    <td className="px-4 py-2 text-sm text-green-600 font-bold">
                                      {item.batches.reduce((sum, b) => sum + (b.sold ?? 0), 0)}
                                    </td>
                                    <td className="px-4 py-2 text-sm font-bold">
                                      {item.batches.reduce((sum, b) => sum + (b.closingStock ?? 0), 0)}
                                    </td>
                                    <td className="px-4 py-2 text-sm text-gray-700 dark:text-gray-200">-</td>
                                    <td className="px-4 py-2 text-sm text-purple-700 dark:text-purple-300 font-bold">NaN%</td>
                                    {/* <td className="px-4 py-2 text-sm font-bold">{formatCurrency(item.batches.reduce((sum, b) => sum + (b.openingValue ?? 0), 0))}</td>
                                    <td className="px-4 py-2 text-sm font-bold">{formatCurrency(item.batches.reduce((sum, b) => sum + (b.purchaseValue ?? 0), 0))}</td>
                                    <td className="px-4 py-2 text-sm font-bold">{formatCurrency(item.batches.reduce((sum, b) => sum + (b.salesValue ?? 0), 0))}</td>
                                    <td className="px-4 py-2 text-sm font-bold">{formatCurrency(item.batches.reduce((sum, b) => sum + (b.closingValue ?? 0), 0))}</td> */}
                                    <td className="px-4 py-2 text-sm font-bold">
                                      {formatCurrency(item.batches.reduce((sum, b) => sum + (b.openingStock ?? 0) * (b.buyingCost ?? 0), 0))}
                                    </td>
                                    <td className="px-4 py-2 text-sm font-bold">
                                      {formatCurrency(item.batches.reduce((sum, b) => sum + (b.purchased ?? 0) * (b.buyingCost ?? 0), 0))}
                                    </td>
                                    <td className="px-4 py-2 text-sm font-bold">
                                      {formatCurrency(item.batches.reduce((sum, b) => sum + (b.sold ?? 0) * (b.salesPrice ?? 0), 0))}
                                    </td>
                                    <td className="px-4 py-2 text-sm font-bold">
                                      {formatCurrency(item.batches.reduce((sum, b) => sum + (b.closingStock ?? 0) * (b.buyingCost ?? 0), 0))}
                                    </td>
                                    <td className="px-4 py-2 text-sm text-gray-700 dark:text-gray-200">-</td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                          </td>
                        </tr>
                      )}
                  </React.Fragment>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>

      {/* Batch Details Summary
      {showBatchDetails && (
        <div className="w-full max-w-6xl mb-6">
          <h2 className="text-xl font-bold mb-4 dark:text-gray-200">
            Batch Details Summary
          </h2>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead className="bg-blue-100 dark:bg-blue-800">
                  <tr>
                    <th className="border-b-2 border-blue-200 px-4 py-3 text-left text-sm font-semibold text-blue-800 dark:text-blue-200">
                      Product Name
                    </th>
                    <th className="border-b-2 border-blue-200 px-4 py-3 text-left text-sm font-semibold text-blue-800 dark:text-blue-200">
                      Total Batches
                    </th>
                    <th className="border-b-2 border-blue-200 px-4 py-3 text-left text-sm font-semibold text-blue-800 dark:text-blue-200">
                      Total Opening Stock
                    </th>
                    <th className="border-b-2 border-blue-200 px-4 py-3 text-left text-sm font-semibold text-blue-800 dark:text-blue-200">
                      Total Purchased
                    </th>
                    <th className="border-b-2 border-blue-200 px-4 py-3 text-left text-sm font-semibold text-blue-800 dark:text-blue-200">
                      Total Sold
                    </th>
                    <th className="border-b-2 border-blue-200 px-4 py-3 text-left text-sm font-semibold text-blue-800 dark:text-blue-200">
                      Total Closing Stock
                    </th>
                    <th className="border-b-2 border-blue-200 px-4 py-3 text-left text-sm font-semibold text-blue-800 dark:text-blue-200">
                      Total Value
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {filteredData
                    .filter((item) => item.hasBatches)
                    .map((item, index) => (
                      <tr
                        key={index}
                        className="hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-200"
                      >
                        <td className="border-b border-gray-100 dark:border-gray-700 px-4 py-3 text-sm text-gray-700 dark:text-gray-200 font-medium">
                          {item.itemName}
                        </td>
                        <td className="border-b border-gray-100 dark:border-gray-700 px-4 py-3 text-sm text-gray-700 dark:text-gray-200">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                            {item.batches.length} Batch
                            {item.batches.length !== 1 ? "es" : ""}
                          </span>
                        </td>
                        <td className="border-b border-gray-100 dark:border-gray-700 px-4 py-3 text-sm text-gray-700 dark:text-gray-200">
                          {item.totalBatchOpeningStock ?? 0}
                        </td>
                        <td className="border-b border-gray-100 dark:border-gray-700 px-4 py-3 text-sm text-gray-700 dark:text-gray-200">
                          {item.totalPurchased ?? 0}
                        </td>
                        <td className="border-b border-gray-100 dark:border-gray-700 px-4 py-3 text-sm text-gray-700 dark:text-gray-200">
                          {item.totalSold ?? 0}
                        </td>
                        <td className="border-b border-gray-100 dark:border-gray-700 px-4 py-3 text-sm text-gray-700 dark:text-gray-200">
                          <span
                            className={`font-medium ${
                              (item.totalBatchClosingStock ?? 0) < lowStockThreshold
                                ? "text-red-600 dark:text-red-400"
                                : "text-green-600 dark:text-green-400"
                            }`}
                          >
                            {item.totalBatchClosingStock ?? 0}
                          </span>
                        </td>
                        <td className="border-b border-gray-100 dark:border-gray-700 px-4 py-3 text-sm text-gray-700 dark:text-gray-200">
                          {formatCurrency(item.totalBatchValue ?? 0)}
                        </td>
                      </tr>
                    ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )} */}

      <div className="w-full max-w-6xl mb-6">
        <h2 className="text-xl font-bold mb-4 dark:text-gray-200">
          Stock Analysis
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg">
            <h3 className="text-lg font-semibold mb-2 dark:text-gray-200">
              Fast Moving Items (Top 5)
            </h3>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={topFastMovingProducts}
                  dataKey="sold"
                  nameKey="itemName"
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  fill="#8884d8"
                  label
                  animationDuration={1000}
                >
                  {topFastMovingProducts.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={COLORS[index % COLORS.length]}
                    />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg">
            <h3 className="text-lg font-semibold mb-2 dark:text-gray-200">
              Slow Moving Items (Top 5)
            </h3>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={topSlowMovingProducts}
                  dataKey="sold"
                  nameKey="itemName"
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  fill="#82ca9d"
                  label
                  animationDuration={1000}
                >
                  {topSlowMovingProducts.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={COLORS[index % COLORS.length]}
                    />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      <div className="w-full max-w-6xl mt-6">
        <div className="bg-white dark:bg-gray-800 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4 rounded-lg shadow-lg text-center">
          <div className="flex flex-col items-center">
            <p className="text-sm font-semibold dark:text-gray-300">
              Authorized By:
            </p>
            <p className="text-sm">[Manager Name]</p>
          </div>
          <div className="flex flex-col items-center">
            <p className="text-sm font-semibold dark:text-gray-300">
              Generated By:
            </p>
            <p className="text-sm">{user?.name || "Unknown User"}</p>
          </div>
          <div className="flex flex-col items-center">
            <p className="text-sm font-semibold dark:text-gray-300">
              Date & Time:
            </p>
            <p className="text-sm">{new Date().toLocaleString()}</p>
          </div>
          <div className="flex flex-col items-center">
            <p className="text-sm font-semibold dark:text-gray-300">
              Report Period:
            </p>
            <p className="text-sm">
              {fromDate} to {toDate}
            </p>
          </div>
          <div className="flex flex-col items-center">
            <p className="text-sm font-semibold dark:text-gray-300">
              Report Generated On:
            </p>
            <p className="text-sm">{new Date().toLocaleString()}</p>
          </div>
          <div className="flex flex-col items-center">
            <p className="text-sm font-semibold dark:text-gray-300">
              Company Contact:
            </p>
            <p className="text-sm">[Your Business Contact Info]</p>
          </div>
          <div className="col-span-1 md:col-span-2 lg:col-span-3 flex justify-center space-x-4 mt-4">
            <button
              onClick={exportToExcel}
              className="px-4 py-2 bg-yellow-500 text-white font-medium rounded-lg shadow-md hover:bg-yellow-600 transition duration-300"
            >
              Export to Excel
            </button>
            <button
              onClick={printReport}
              className="px-4 py-2 bg-green-500 text-white font-medium rounded-lg shadow-md hover:bg-green-600 transition duration-300"
            >
              Print Report
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StockReport;
