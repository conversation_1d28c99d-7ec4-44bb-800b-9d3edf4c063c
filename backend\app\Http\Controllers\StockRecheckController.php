<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\StockRecheck;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Http\Controllers\StockReportController;
use App\Services\ClosingStockService;

class StockRecheckController extends Controller
{
    public function index(Request $request)
    {
        Log::info('Fetching stock recheck records with filters:', $request->all());

        try {
            $query = StockRecheck::with(['user:id,name', 'productVariant']);

            if ($request->has('item_code') && $request->item_code !== '') {
                $query->where('item_code', 'like', '%' . $request->item_code . '%');
            }

            if ($request->has('item_name') && $request->item_name !== '') {
                $query->where('item_name', 'like', '%' . $request->item_name . '%');
            }

            if ($request->has('location') && $request->location !== '') {
                $query->where('location', 'like', '%' . $request->location . '%');
            }

            if ($request->has('batch_number') && $request->batch_number !== '') {
                $query->where('batch_number', 'like', '%' . $request->batch_number . '%');
            }

            if ($request->has('recheck_type') && $request->recheck_type !== '') {
                $query->where('recheck_type', $request->recheck_type);
            }

            $rechecks = $query->orderBy('created_at', 'desc')->get();

            Log::info('Stock recheck records fetched:', $rechecks->toArray());

            return response()->json($rechecks, 200);
        } catch (\Exception $e) {
            Log::error('Error fetching stock recheck records: ' . $e->getMessage() . "\nStack Trace: " . $e->getTraceAsString());
            return response()->json(['error' => 'Failed to fetch stock recheck records: ' . $e->getMessage()], 500);
        }
    }

    public function destroy($id)
    {
        Log::info('Deleting stock recheck with ID: ' . $id);

        try {
            $recheck = StockRecheck::findOrFail($id);
            $recheck->delete();

            Log::info('Deleted stock recheck with ID: ' . $id);

            return response()->json(['message' => 'Stock recheck deleted successfully'], 200);
        } catch (\Exception $e) {
            Log::error('Error deleting stock recheck: ' . $e->getMessage() . "\nStack Trace: " . $e->getTraceAsString());
            return response()->json(['error' => 'Failed to delete stock recheck: ' . $e->getMessage()], 500);
        }
    }

    public function store(Request $request)
    {
        Log::info('Saving stock recheck with data:', $request->all());

        try {
            $validated = $request->validate([
                'item_code' => 'required|string',
                'item_name' => 'required|string',
                'system_qty' => 'required|integer',
                'store_qty' => 'required|integer',
                'difference' => 'required|integer',
                'location' => 'required|string',
                'batch_number' => 'nullable|string',
                'product_variant_id' => 'nullable|integer|exists:product_variants,product_variant_id',
                'expiry_date' => 'nullable|string',
                'recheck_type' => 'required|string|in:product,batch',
                'remarks' => 'nullable|string',
                'status' => 'required|string|in:OK,Discrepancy',
                'update_actual_stock' => 'boolean',
                'user_id' => 'nullable|integer|exists:users,id',
            ]);

            // Convert expiry_date to proper format if provided
            if (!empty($validated['expiry_date'])) {
                try {
                    $validated['expiry_date'] = Carbon::parse($validated['expiry_date'])->format('Y-m-d');
                } catch (\Exception $e) {
                    Log::warning('Failed to parse expiry_date: ' . $validated['expiry_date']);
                    $validated['expiry_date'] = null;
                }
            }

            $product = Product::where('item_code', $validated['item_code'])->first();
            if (!$product) {
                return response()->json(['error' => 'Product not found'], 404);
            }

            // Validate batch-level recheck if needed
            if ($validated['recheck_type'] === 'batch') {
                if (empty($validated['batch_number']) && empty($validated['product_variant_id'])) {
                    return response()->json(['error' => 'Batch number or product variant ID required for batch recheck'], 400);
                }

                if ($validated['product_variant_id']) {
                    $variant = ProductVariant::find($validated['product_variant_id']);
                    if (!$variant) {
                        return response()->json(['error' => 'Product variant not found'], 404);
                    }
                }
            }

            DB::beginTransaction();

            // Find existing recheck based on type
            if ($validated['recheck_type'] === 'batch') {
                $recheck = StockRecheck::where('item_code', $validated['item_code'])
                    ->where('location', $validated['location'])
                    ->where('recheck_type', 'batch')
                    ->where(function($query) use ($validated) {
                        if ($validated['product_variant_id']) {
                            $query->where('product_variant_id', $validated['product_variant_id']);
                        } else {
                            $query->where('batch_number', $validated['batch_number']);
                        }
                    })
                    ->first();
            } else {
                $recheck = StockRecheck::where('item_code', $validated['item_code'])
                    ->where('location', $validated['location'])
                    ->where('recheck_type', 'product')
                    ->first();
            }

            // Set corrected_closing_stock based on update_actual_stock
            $validated['corrected_closing_stock'] = $validated['update_actual_stock'] ? $validated['store_qty'] : null;

            if ($recheck) {
                $recheck->update($validated);
                Log::info('Updated existing ' . $validated['recheck_type'] . ' recheck for item_code: ' . $validated['item_code'] . 
                          ($validated['batch_number'] ? ' batch: ' . $validated['batch_number'] : '') .
                          ($validated['product_variant_id'] ? ' variant_id: ' . $validated['product_variant_id'] : '') .
                          ($validated['update_actual_stock'] ? ' with corrected_closing_stock: ' . $validated['store_qty'] : ' with corrected_closing_stock cleared'));
            } else {
                $created = StockRecheck::create($validated);
                Log::info('Created new ' . $validated['recheck_type'] . ' recheck for item_code: ' . $validated['item_code'] . 
                          ($validated['batch_number'] ? ' batch: ' . $validated['batch_number'] : '') .
                          ($validated['product_variant_id'] ? ' variant_id: ' . $validated['product_variant_id'] : '') .
                          ($validated['update_actual_stock'] ? ' with corrected_closing_stock: ' . $validated['store_qty'] : ' with corrected_closing_stock null'));
                Log::info('Created recheck data: ', $created->toArray());
            }

            // Update closing stock for both batch and product level rechecks using centralized service
            $closingStockService = new ClosingStockService();
            
            if ($validated['recheck_type'] === 'batch' && $validated['product_variant_id']) {
                if ($validated['update_actual_stock']) {
                    // Set closing stock to the rechecked value
                    $variant = ProductVariant::find($validated['product_variant_id']);
                    if ($variant) {
                        $variant->update(['closing_stock_quantity' => $validated['store_qty']]);
                        Log::info("Updated variant {$variant->product_variant_id} closing stock to: {$validated['store_qty']}");
                        
                        // Also update the product closing stock
                        $closingStockService->updateProductClosingStock($variant->product);
                    }
                } else {
                    // Recalculate closing stock based on transactions
                    $closingStockService->updateClosingStockOnTransaction($product->product_id, $validated['product_variant_id']);
                }
            } elseif ($validated['recheck_type'] === 'product' && $validated['update_actual_stock']) {
                // Update product-level closing stock
                $product = Product::where('item_code', $validated['item_code'])->first();
                if ($product) {
                    $product->update(['closing_stock_quantity' => $validated['store_qty']]);
                    Log::info("Updated product {$product->product_id} closing stock to: {$validated['store_qty']}");
                    
                    // Also update all variants for this product
                    $closingStockService->updateClosingStockOnTransaction($product->product_id, null);
                }
            }

            DB::commit();

            return response()->json(['message' => 'Stock recheck saved successfully', 'data' => $validated], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error saving stock recheck: ' . $e->getMessage() . "\nStack Trace: " . $e->getTraceAsString());
            return response()->json(['error' => 'Failed to save stock recheck: ' . $e->getMessage()], 500);
        }
    }

    public function update(Request $request, $id)
    {
        Log::info('Updating stock recheck with ID: ' . $id . ', data:', $request->all());

        try {
            $recheck = StockRecheck::findOrFail($id);

            $validated = $request->validate([
                'item_code' => 'required|string',
                'item_name' => 'required|string',
                'system_qty' => 'required|integer',
                'store_qty' => 'required|integer',
                'difference' => 'required|integer',
                'location' => 'required|string',
                'batch_number' => 'nullable|string',
                'product_variant_id' => 'nullable|integer|exists:product_variants,product_variant_id',
                'expiry_date' => 'nullable|string',
                'recheck_type' => 'required|string|in:product,batch',
                'remarks' => 'nullable|string',
                'status' => 'required|string|in:OK,Discrepancy',
                'update_actual_stock' => 'boolean',
                'user_id' => 'nullable|integer|exists:users,id',
            ]);

            // Convert expiry_date to proper format if provided
            if (!empty($validated['expiry_date'])) {
                try {
                    $validated['expiry_date'] = Carbon::parse($validated['expiry_date'])->format('Y-m-d');
                } catch (\Exception $e) {
                    Log::warning('Failed to parse expiry_date: ' . $validated['expiry_date']);
                    $validated['expiry_date'] = null;
                }
            }

            $product = Product::where('item_code', $validated['item_code'])->first();
            if (!$product) {
                return response()->json(['error' => 'Product not found'], 404);
            }

            // Validate batch-level recheck if needed
            if ($validated['recheck_type'] === 'batch') {
                if (empty($validated['batch_number']) && empty($validated['product_variant_id'])) {
                    return response()->json(['error' => 'Batch number or product variant ID required for batch recheck'], 400);
                }

                if ($validated['product_variant_id']) {
                    $variant = ProductVariant::find($validated['product_variant_id']);
                    if (!$variant) {
                        return response()->json(['error' => 'Product variant not found'], 404);
                    }
                }
            }

            DB::beginTransaction();

            // Set corrected_closing_stock based on update_actual_stock
            $validated['corrected_closing_stock'] = $validated['update_actual_stock'] ? $validated['store_qty'] : null;

            $recheck->update($validated);

            Log::info('Updated ' . $validated['recheck_type'] . ' recheck for item_code: ' . $validated['item_code'] . 
                      ($validated['batch_number'] ? ' batch: ' . $validated['batch_number'] : '') .
                      ($validated['update_actual_stock'] ? ' with corrected_closing_stock: ' . $validated['store_qty'] : ' with corrected_closing_stock cleared'));

            // Update closing stock for both batch and product level rechecks using centralized service
            $closingStockService = new ClosingStockService();
            
            if ($validated['recheck_type'] === 'batch' && $validated['product_variant_id']) {
                if ($validated['update_actual_stock']) {
                    // Set closing stock to the rechecked value
                    $variant = ProductVariant::find($validated['product_variant_id']);
                    if ($variant) {
                        $variant->update(['closing_stock_quantity' => $validated['store_qty']]);
                        Log::info("Updated variant {$variant->product_variant_id} closing stock to: {$validated['store_qty']}");
                        
                        // Also update the product closing stock
                        $closingStockService->updateProductClosingStock($variant->product);
                    }
                } else {
                    // Recalculate closing stock based on transactions
                    $closingStockService->updateClosingStockOnTransaction($product->product_id, $validated['product_variant_id']);
                }
            } elseif ($validated['recheck_type'] === 'product' && $validated['update_actual_stock']) {
                // Update product-level closing stock
                $product = Product::where('item_code', $validated['item_code'])->first();
                if ($product) {
                    $product->update(['closing_stock_quantity' => $validated['store_qty']]);
                    Log::info("Updated product {$product->product_id} closing stock to: {$validated['store_qty']}");
                    
                    // Also update all variants for this product
                    $closingStockService->updateClosingStockOnTransaction($product->product_id, null);
                }
            }

            DB::commit();

            return response()->json(['message' => 'Stock recheck updated successfully', 'data' => $validated], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating stock recheck: ' . $e->getMessage() . "\nStack Trace: " . $e->getTraceAsString());
            return response()->json(['error' => 'Failed to update stock recheck: ' . $e->getMessage()], 500);
        }
    }

    public function locations(Request $request)
    {
        Log::info('Fetching locations');
        try {
            $productLocations = \App\Models\Product::distinct()->pluck('store_location')->filter();
            $variantLocations = \App\Models\ProductVariant::distinct()->pluck('store_location')->filter();
            $locations = $productLocations->merge($variantLocations)->unique()->values();
            Log::info('Locations fetched:', $locations->toArray());
            return response()->json($locations, 200);
        } catch (\Exception $e) {
            Log::error('Error fetching locations: ' . $e->getMessage() . "\nStack Trace: " . $e->getTraceAsString());
            return response()->json(['error' => 'Failed to fetch locations: ' . $e->getMessage()], 500);
        }
    }

    public function getProductBatches(Request $request, $itemCode)
    {
        Log::info('Fetching batches for item code: ' . $itemCode);

        try {
            $product = Product::where('item_code', $itemCode)->first();
            if (!$product) {
                return response()->json(['error' => 'Product not found'], 404);
            }

            $batches = ProductVariant::where('product_id', $product->product_id)
                ->select('product_variant_id', 'batch_number', 'expiry_date', 'closing_stock_quantity', 'store_location')
                ->get();

            Log::info('Batches fetched for item code ' . $itemCode . ':', $batches->toArray());
            return response()->json($batches, 200);
        } catch (\Exception $e) {
            Log::error('Error fetching batches: ' . $e->getMessage() . "\nStack Trace: " . $e->getTraceAsString());
            return response()->json(['error' => 'Failed to fetch batches: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Update ProductVariant closing stock quantity instantly when recheck is saved
     */
    private function updateVariantClosingStock($productVariantId, $newClosingStock)
    {
        try {
            $variant = ProductVariant::find($productVariantId);
            if ($variant) {
                $oldClosingStock = $variant->closing_stock_quantity ?? 0;
                $variant->update(['closing_stock_quantity' => $newClosingStock]);
                
                Log::info("Instantly updated variant {$productVariantId} closing stock: {$oldClosingStock} -> {$newClosingStock}");
                return true;
            } else {
                Log::warning("ProductVariant with ID {$productVariantId} not found for closing stock update");
                return false;
            }
        } catch (\Exception $e) {
            Log::error("Error updating variant closing stock: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Recalculate and update ProductVariant closing stock when recheck update_actual_stock is unchecked
     */
    private function recalculateVariantClosingStock($productVariantId)
    {
        try {
            $variant = ProductVariant::find($productVariantId);
            if ($variant) {
                // Use the same calculation logic from StockReportController
                $closingStock = $this->calculateVariantClosingStockFromTransactions($variant);
                $oldClosingStock = $variant->closing_stock_quantity ?? 0;
                $variant->update(['closing_stock_quantity' => $closingStock]);
                
                Log::info("Recalculated variant {$productVariantId} closing stock: {$oldClosingStock} -> {$closingStock}");
                return true;
            }
            return false;
        } catch (\Exception $e) {
            Log::error("Error recalculating variant closing stock: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Calculate closing stock for a variant based on all transactions (without recheck adjustments)
     */
    private function calculateVariantClosingStockFromTransactions(ProductVariant $variant)
    {
        $closingStock = $variant->opening_stock_quantity ?? 0;

        // Add purchases
        $totalPurchased = DB::table('purchase_items')
            ->where('product_variant_id', $variant->product_variant_id)
            ->sum('quantity') ?? 0;
        $closingStock += $totalPurchased;

        // Subtract sales
        $totalSoldInvoice = DB::table('invoice_items')
            ->where('product_variant_id', $variant->product_variant_id)
            ->sum('quantity') ?? 0;
        $closingStock -= $totalSoldInvoice;

        $totalSoldPOS = DB::table('sale_items')
            ->where('product_variant_id', $variant->product_variant_id)
            ->sum('quantity') ?? 0;
        $closingStock -= $totalSoldPOS;

        // Add sales returns
        $totalReturned = DB::table('sales_return_items')
            ->join('sales_returns', 'sales_return_items.sales_return_id', '=', 'sales_returns.id')
            ->where('sales_return_items.product_variant_id', $variant->product_variant_id)
            ->where('sales_returns.status', 'approved')
            ->sum('sales_return_items.quantity') ?? 0;
        $closingStock += $totalReturned;

        // Subtract purchase returns
        $totalPurchaseReturned = DB::table('purchase_return_items')
            ->join('purchase_returns', 'purchase_return_items.purchase_return_id', '=', 'purchase_returns.id')
            ->where('purchase_return_items.product_variant_id', $variant->product_variant_id)
            ->where('purchase_returns.status', 'approved')
            ->sum('purchase_return_items.quantity') ?? 0;
        $closingStock -= $totalPurchaseReturned;

        return max(0, $closingStock);
    }

    /**
     * Get current system quantity for stock recheck
     */
    public function getCurrentSystemQuantity(Request $request)
    {
        Log::info('Getting current system quantity for stock recheck:', $request->all());

        try {
            $itemCode = $request->input('item_code');
            $location = $request->input('location');
            $productVariantId = $request->input('product_variant_id');
            $batchNumber = $request->input('batch_number');
            $recheckType = $request->input('recheck_type', 'product');

            if (!$itemCode || !$location) {
                return response()->json(['error' => 'Item code and location are required'], 400);
            }

            $currentSystemQty = 0;

            if ($recheckType === 'batch' && $productVariantId) {
                // For batch-level recheck - use closing_stock_quantity from product_variants table
                $variant = ProductVariant::find($productVariantId);
                if (!$variant) {
                    return response()->json(['error' => 'Product variant not found'], 404);
                }

                Log::info("Processing batch variant: {$variant->product_variant_id}, batch: {$variant->batch_number}");
                Log::info("Variant closing stock from database: {$variant->closing_stock_quantity}");

                $currentSystemQty = $variant->closing_stock_quantity ?? 0;
            } else {
                // For product-level recheck - use closing_stock_quantity from products table
                $product = Product::where('item_code', $itemCode)->first();
                if (!$product) {
                    return response()->json(['error' => 'Product not found'], 404);
                }

                Log::info("Processing product: {$product->product_id}, item_code: {$itemCode}");
                Log::info("Product closing stock from database: {$product->closing_stock_quantity}");

                $currentSystemQty = $product->closing_stock_quantity ?? 0;
            }

            $finalSystemQty = max(0, $currentSystemQty);
            Log::info("Final system quantity for {$itemCode} at {$location}: {$finalSystemQty}");

            return response()->json([
                'current_system_qty' => $finalSystemQty,
                'item_code' => $itemCode,
                'location' => $location,
                'product_variant_id' => $productVariantId,
                'batch_number' => $batchNumber,
                'recheck_type' => $recheckType,
                'debug_info' => [
                    'raw_closing_stock' => $currentSystemQty,
                    'final_after_max' => $finalSystemQty
                ]
            ], 200);

        } catch (\Exception $e) {
            Log::error('Error getting current system quantity: ' . $e->getMessage() . "\nStack Trace: " . $e->getTraceAsString());
            return response()->json(['error' => 'Failed to get current system quantity: ' . $e->getMessage()], 500);
        }
    }
}