<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\ClosingStockService;
use Illuminate\Support\Facades\Log;

class RecalculateClosingStock extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stock:recalculate-closing {--force : Force recalculation even if closing stock exists}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Recalculate and update closing stock for all products and variants';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting closing stock recalculation...');
        
        try {
            $closingStockService = new ClosingStockService();
            
            $this->info('Calculating closing stock for all products and variants...');
            
            $result = $closingStockService->updateAllClosingStock();
            
            $this->info("✅ Closing stock recalculation completed successfully!");
            $this->info("📊 Updated {$result['variants_updated']} product variants");
            $this->info("📊 Updated {$result['products_updated']} products");
            
            if ($result['variants_updated'] === 0 && $result['products_updated'] === 0) {
                $this->warn('⚠️  No updates were made. All closing stock values are already up to date.');
            }
            
            return 0;
            
        } catch (\Exception $e) {
            $this->error('❌ Error during closing stock recalculation: ' . $e->getMessage());
            Log::error('Error in RecalculateClosingStock command: ' . $e->getMessage());
            return 1;
        }
    }
} 