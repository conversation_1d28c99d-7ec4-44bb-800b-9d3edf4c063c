<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductVariant extends Model
{
    use HasFactory, SoftDeletes;

    protected $primaryKey = 'product_variant_id';

    protected $fillable = [
        'product_id',
        'batch_number',
        'expiry_date',
        'buying_cost',
        'sales_price',
        'minimum_price',
        'wholesale_price',
        'barcode',
        'mrp',
        'minimum_stock_quantity',
        'opening_stock_quantity',
        'closing_stock_quantity',
        'opening_stock_value',
        'store_location',
        'cabinet',
        'row',
        'extra_fields',
    ];

    protected $casts = [
        'buying_cost' => 'float',
        'sales_price' => 'float',
        'minimum_price' => 'float',
        'wholesale_price' => 'float',
        'mrp' => 'float',
        'minimum_stock_quantity' => 'float',
        'opening_stock_quantity' => 'float',
        'closing_stock_quantity' => 'float',
        'opening_stock_value' => 'float',
        'extra_fields' => 'array',
        'expiry_date' => 'date',
    ];

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id', 'product_id');
    }

    public function deletedByUser()
    {
        return $this->belongsTo(User::class, 'deleted_by')->withTrashed();
    }

    protected static function boot()
    {
        parent::boot();
        
        // When creating a new product variant, set closing_stock_quantity to opening_stock_quantity
        static::creating(function ($model) {
            // Set closing_stock_quantity to opening_stock_quantity if not already set
            if (!isset($model->closing_stock_quantity) || is_null($model->closing_stock_quantity)) {
                $model->closing_stock_quantity = $model->opening_stock_quantity ?? 0;
            }
        });
        
        // When a variant is created, also update the parent product's closing_stock_quantity
        static::created(function ($model) {
            if ($model->product) {
                // Update the product's closing stock by summing all its variants
                $totalClosingStock = $model->product->variants()->sum('closing_stock_quantity');
                $model->product->update(['closing_stock_quantity' => $totalClosingStock]);
            }
        });
    }
}
