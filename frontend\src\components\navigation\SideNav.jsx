import React, { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { useAuth } from "../../context/NewAuthContext";
import {
  LayoutDashboard,
  ChevronDown,
  ChevronRight,
  Package,
  ShoppingCart,
  FileText,
  Settings as SettingsIcon,
  DollarSign,
  BookOpen,
  BarChart2,
  Menu,
  PanelLeftClose,
  Box,
  Tag,
  Layers,
  MapPin,
  Search,
  User,
  RefreshCcw,
  RotateCcw,
  ShoppingBag,
  ClipboardPen,
  ClipboardList,
  BadgeDollarSign,
  ShoppingBasket,
  ClipboardPenLine,
  ClipboardCheck,
  Undo,
  Wrench,
  ShieldCheck,
  BaggageClaim,
  Trash2,
  TrendingUp,
  Calendar,
  Barcode,
  FileBarChart,
  Table,
  UserRound,
  Store,
  Recycle,
  BadgePercent,
  LayoutList,
  Castle,
  SquareCheckBig,
  Users,
  BookUser,
  CalendarCheck,
  Receipt,
  Truck,
  Book,
  Clock,
  HelpCircle,
  MessageSquare,
  Building2,
  PlusSquare,
  BarChart2Icon,
  Boxes,
  FileBarChart2Icon,
  Wallet,
  PackagePlus,
  SendHorizontal,
  Sparkles,
  Banknote,
  LineChart,
  Scale,
  Table2,
  DollarSignIcon,
  Coins,
  Building,
  Download,
  LucideBanknoteX,
} from "lucide-react";
import { BsCashCoin } from "react-icons/bs";



const SideNav = ({ isPosOpen }) => {
  const { user, checkPermission } = useAuth();
  const location = useLocation();
  const [isNavVisible, setIsNavVisible] = useState(true);
  const [openMenus, setOpenMenus] = useState({});
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredItems, setFilteredItems] = useState([]);

  const hasPermission = (itemPath) => {
    if (checkPermission(itemPath)== true) {
      return user.role;
    }
    return '';
  }


  // Role-based navigation items
  const navItems = [
    {
      path: "/dashboard",
      icon: LayoutDashboard,
      label: "Dashboard",
      roles: [hasPermission('dashboard')],
    },
    {
      path: "/items",
      icon: Package,
      label: "Items",
      roles: [hasPermission('items')|| hasPermission('expiry') || hasPermission('suppliers') || hasPermission('categories') || hasPermission('units') || hasPermission('store-locations')||hasPermission('BarcodePage')],
      subItems: [
        {
          path: "/items",
          label: "Items",
          icon: Box,
          roles: [hasPermission('items')],
        },
        {
          path: "/expiry",
          label: "Expiry",
          icon: CalendarCheck,
          roles: [hasPermission('expiry')],
        },
        {
          path: "/suppliers",
          label: "Suppliers",
          icon: Users,
          roles: [hasPermission('suppliers')],
        },
        {
          path: "/categories",
          label: "Categories",
          icon: Tag,
          roles: [hasPermission('categories')],
        },
        {
          path: "/companies",
          label: "Companies",
          icon: Castle,
          roles: [hasPermission('companies')],
        },
        {
          path: "/units",
          label: "Units",
          icon: Layers,
          roles: [hasPermission('units')],
        },
        {
          path: "/store-locations",
          label: "Store Locations",
          icon: MapPin,
          roles: [hasPermission('store-locations')],
        },
        { path: "/BarcodePage", label: "Barcode", icon: Barcode,roles: [hasPermission('BarcodePage')] },
        {
          path: "/itemageanalyze",
          label: "Item Age Analyze",
          icon: CalendarCheck,
          roles: [hasPermission('itemageanalyze')],
        },
      ],
    },
    {
      path: "/sales",
      icon: ShoppingBag,
      label: "Sales",
      roles: [hasPermission('sales')|| hasPermission('tax') || hasPermission('SalesReturn') || hasPermission('Customers')],
      subItems: [
        {
          path: "/sales",
          label: "Sales",
          icon: BadgeDollarSign,
          roles: [hasPermission('sales')],
        },
        {
          path: "/tax",
          label: "Tax",
          icon: BadgePercent,
          roles: [hasPermission('tax')],
        },
        {
          path: "/SalesReturn",
          label: "Sales Return",
          icon: RefreshCcw,
          roles: [hasPermission('SalesReturn')],
        },
        {
          path: "/Customers",
          label: "Customers",
          icon: User,
          roles: [hasPermission('Customers')],
        },
      ],
    },
    {
      path: "/purchasing",
      icon: ShoppingCart,
      label: "Purchasing",
      roles: [hasPermission('purchasing')|| hasPermission('PurchaseOrder') || hasPermission('PurchaseReturn')],
      subItems: [
        {
          path: "/purchasing",
          label: "Purchasing",
          icon: ShoppingBasket,
          roles: [hasPermission('purchasing')],
        },
        // {
        //   path: "/PurchaseInvoice",
        //   label: "Purchasing Invoice",
        //   icon: ClipboardPenLine,
        //   roles: ["admin", "manager"],
        // },
        {
          path: "/PurchaseOrder",
          label: "Purchase Order",
          icon: FileText,
          roles: [hasPermission('PurchaseOrder')],
        },
        {
          path: "/PurchaseReturn",
          label: "Purchase Return",
          icon: RotateCcw,
          roles: [hasPermission('PurchaseReturn')],
        },
      ],
    },

    {
      path: "/financial-accounting",
      icon: Book,
      label: "Financial Accounting",
      roles: [hasPermission('financial-accounting')|| hasPermission('Outstanding') || hasPermission('bill-by-bill-collection') || hasPermission('aging-analysis') || hasPermission('payable') || hasPermission('profit-and-loss-report') || hasPermission('cash-in-hand') || hasPermission('bank-account') || hasPermission('balance-sheet') || hasPermission('trial-balance') ],
      subItems: [
        {
          path: "/outstanding",
          label: "Outstanding",
          icon: DollarSign,
          roles: [hasPermission('Outstanding')],
        },
        {
          path: "/bill-by-bill-collection",
          label: "Bill by Bill Collection",
          icon: FileText,
          roles: [hasPermission('bill-by-bill-collection')],
        },
        {
          path: "/aging-analysis",
          label: "Aging Analysis",
          icon: Clock,
          roles: [hasPermission('aging-analysis')],
        },
        {
          path: "/payable",
          label: "Payable",
          icon: Coins,
          roles: [hasPermission('payable')],
        },
        {
          path: "/profit-and-loss-report",
          label: "Profit and Loss Report",
          icon: LineChart,
          roles: [hasPermission('profit-and-loss-report')],
        },
        {
          path: "/cash-in-hand",
          label: "Cash in hand Statement",
          icon: BsCashCoin,
          roles: [hasPermission('cash-in-hand')],
        },
        {
          path: "/bank-account",
          label: "Bank Account Statement",
          icon: LucideBanknoteX,
          roles: [hasPermission('bank-account')],
        },
        {
          path: "/balance-sheet",
          label: "Balance Sheet",
          icon: Scale,
          roles: [hasPermission('balance-sheet')],
        },
        {
          path: "/trial-balance",
          label: "Trial Balance",
          icon: Table2,
          roles: [hasPermission('trial-balance')],
        },
      ],
    },

    {
      path: "/voucher",
      icon: Wallet,
      label: "Voucher",
      roles: [hasPermission('voucher')|| hasPermission('voucher/ReceiveVoucher') || hasPermission('voucher/PaymentVoucher')],
      subItems: [
        {
          path: "/voucher/ReceiveVoucher",
          label: "Receive Voucher",
          icon: PackagePlus,
          roles: [hasPermission('voucher/ReceiveVoucher')],
        },
        {
          path: "/voucher/PaymentVoucher",
          label: "Payment Voucher",
          icon: SendHorizontal,
          roles: [hasPermission('voucher/PaymentVoucher')],
        },
      ],
    },
    {
      path: "ledger/",
      icon: BookOpen,
      label: "Ledger",
      roles: [ hasPermission('ledger/new-ledger') || hasPermission('ledger/statement')],
      subItems: [
        {
          path: "/ledger/new-ledger",
          label: "New Ledger",
          icon: Sparkles,
          roles: [hasPermission('ledger/new-ledger')],
        },
        {
          path: "/ledger/statement",
          label: "Statement",
          icon: TrendingUp,
          roles: [hasPermission('ledger/statement')],
        },
        {
          path: "/ledger/cheque-statement",
          label: "Cheques Statement",
          icon: Banknote,
          roles: [hasPermission('ledger/statement')],
        },
      ],
    },
    // {
    //   path: "/ledger",
    //   icon: BookOpen,
    //   label: "Ledger",
    //   roles: ["admin", "manager"],
    // },
    {
      path: "/profit",
      icon: DollarSign,
      label: "Profit",
      roles: [hasPermission('profit')|| hasPermission('ItemWiseProfit') || hasPermission('BillWiseProfit') || hasPermission('CustomerWiseProfit') || hasPermission('SupplierWiseProfit') || hasPermission('StoreAndLocationWiseProfit') || hasPermission('CategoryWiseProfit')],
      subItems: [
        {
          path: "/ItemWiseProfit",
          label: "Daily Profit",
          icon: TrendingUp,
          roles: [hasPermission('ItemWiseProfit')],
        },
        {
          path: "/BillWiseProfit",
          label: "Bill Wise Profit",
          icon: Receipt,
          roles: [hasPermission('BillWiseProfit')],
        },
        {
          path: "/CustomerWiseProfit",
          label: "Customer Wise Profit",
          icon: Users,
          roles: [hasPermission('CustomerWiseProfit')],
        },
        {
          path: "/SupplierWiseProfit",
          label: "Supplier Wise Profit",
          icon: Truck,
          roles: [hasPermission('SupplierWiseProfit')],
        },
        {
          path: "/StoreAndLocationWiseProfit",
          label: "Store and Location Wise Profit",
          icon: MapPin,
          roles: [hasPermission('StoreAndLocationWiseProfit')],
        },
        {
          path: "/CategoryWiseProfit",
          label: "Category Wise Profit",
          icon: Tag,
          roles: [hasPermission('CategoryWiseProfit')],
        },
        {
          path: "/CompanyWiseProfit",
          label: "Company Wise Profit",
          icon: Building,
          roles: [hasPermission('CompanyWiseProfit')],
        },

      ],
    },
    {
      path: "/report",
      icon: BarChart2,
      label: "Reports",
      roles: [hasPermission('report')|| hasPermission('StockReport') || hasPermission('ItemWiseStockReport') || hasPermission('StockRecheck') || hasPermission('Monthly-wise-Report') || hasPermission('reports/registry')|| hasPermission('reports/dailysales')],
      subItems: [
        {
          path: "/StockReport",
          label: "Stock Reports",
          icon: BarChart2,
          roles: [hasPermission('StockReport')],
        },
        {
          path: "/ItemWiseStockReport",
          label: "Item Wise Report",
          icon: FileBarChart,
          roles: [hasPermission('ItemWiseStockReport')],
        },
        {
          path: "/StockRecheck",
          label: "Stock Re-Check",
          icon: BarChart2,
          roles: [hasPermission('StockRecheck')],
        },
        {
          path: "/UserActivityReport",
          label: "User ActivityReport",
          icon: BarChart2,
          roles: [hasPermission('UserActivityReport')],
        },
        {
          path: "/Monthly-wise-Report",
          label: "Monthly wise Report",
          icon: BarChart2,
          roles: [hasPermission('Monthly-wise-Report')],
        },
        {
          path: "/reports/registry",
          label: "Registry Reports",
          icon: Receipt,
          roles: [hasPermission('reports/registry')],
        },
        {
          path: "/reports/dailysales",
          label: "Dailysales Report",
          icon: Receipt,
          roles: [hasPermission('reports/dailysales')],
        },
      ],
    },
    {
      path: "/production",
      icon: Store,
      label: "Production Management",
      roles: [hasPermission('production')],
      subItems: [{ path: "/Production", label: "Production", icon: Table }],
    },
    {
      path: "/Approvels",
      label: "Approvels",
      icon: ShieldCheck,
      roles: [hasPermission('Approvels')],
    },

    // {
    //   path: "/StockTransfer",
    //   icon: BaggageClaim,
    //   label: "StockTransfer",
    //   roles: ["admin"],
    // },

    {
      path: "/DiscountScheam",
      icon: BadgePercent,
      label: "DiscountScheme",
      roles: [hasPermission('DiscountScheam')],
    },

    {
      path: "/Sms-template",
      icon: MessageSquare,
      label: "SMS Template",
      roles: [hasPermission('Sms-template')],
    },

    {
      path: "/TaskManager",
      icon: LayoutList,
      label: "Task Management",
      roles: [hasPermission('TaskManager')|| hasPermission('HomePage') || hasPermission('TasksPage') || hasPermission('ProjectsPage') || hasPermission('ReportPage') || hasPermission('SubtasksPage')],
      subItems: [
        {
          path: "/HomePage",
          label: "Taskmanager",
          icon: SquareCheckBig,
          roles: [hasPermission('HomePage')],
        },
        {
          path: "/ProjectsPage",
          label: "Projects",
          icon: SquareCheckBig,
          roles: [hasPermission('ProjectsPage')],
        },
        {
          path: "/TasksPage",
          label: "Tasks",
          icon: SquareCheckBig,
          roles: [hasPermission('TasksPage')],
        },
        {
          path: "/SubtasksPage",
          label: "Sub Tasks",
          icon: SquareCheckBig,
          roles: [hasPermission('SubtasksPage')],
        },
        
        {
          path: "/ReportPage",
          label: "Reports",
          icon: SquareCheckBig,
          roles: [hasPermission('ReportPage')],
        },
        // Add this nav item for My Assignments
      {
        path: "/AssigneePage",
        icon: UserRound,
        label: "Assignments",
        roles: [user?.role || "user"], // visible to all logged-in users
      },
        
      ],
    },
    {
      path: "/Staff",
      icon: Users,
      label: "Staff Management",
      roles: [hasPermission('Staff')|| hasPermission('StaffManagement')],
      subItems: [
        {
          path: "/StaffManagement",
          label: "Staff Panel",
          icon: BookUser,
          roles: [hasPermission('StaffManagement')],
        },
      ],
    },

    {
      path: "/loyalty",
      icon: BadgePercent,
      label: "Loyalty",
      roles: [hasPermission('loyalty')|| hasPermission('loyalty/report') || hasPermission('loyalty/generate-card')  || hasPermission('loyalty/design-card')],
      subItems: [
        {
          path: "/loyalty/report",
          label: "Loyalty Report",
          icon: FileBarChart,
          roles: [hasPermission('loyalty/report')],
        },
        {
          path: "/loyalty/generate-card",
          label: "Loyalty Card Generate",
          icon: Barcode,
          roles: [hasPermission('loyalty/generate-card')],
        },

        // {
        //   path: "Customers",
        //   label: "Loyalty Card membership",
        //   icon: Users,
        //   roles: [hasPermission('Customers')],
        // },
        {
          path: "/loyalty/design-card",
          label: "Loyalty Card Design",
          icon: LayoutList,
          roles: [hasPermission('loyalty/design-card')],
        },
      ],
    },
    {
      path: "/RecycleBin",
      label: "Recycle Bin",
      icon: Trash2,
      recycle: [hasPermission('RecycleBin')],
    },
    {
      path: "/UserManagement",
      icon: UserRound,
      label: "User Management",
      roles: [hasPermission('UserManagement')|| hasPermission('RoleList') || hasPermission('UserList') ],
      subItems: [
        // { path: "/AdminAccess", label: "Admin Panel", roles: ['admin'] },
        { path: "/RoleList", label: "Roles", icon: Wrench, roles: [hasPermission('RoleList')] },
        { path: "/UserList", label: "Users", icon: Users, roles: [hasPermission('UserList')] },
        
      ],
    },

    {
      path: "/branch-management",
      icon: Building2,
      label: "Branch Mangement",
      roles: [hasPermission('branch-management')||  hasPermission('branch-management/create-branch') || hasPermission('branch-management/sales-report') || hasPermission('branch-management/stock-report') || hasPermission('branch-management/stock-transfer') || hasPermission('branch-management/receive-stock')],
      subItems: [
        {
          path: "/branch-management/create-branch",
          label: "Create Branch",
          icon: PlusSquare,
          roles: [hasPermission('branch-management/create-branch')],
        },
        {
          path: "/branch-management/sales-report",
          label: "Sales Report",
          icon: FileBarChart2Icon,
          roles: [hasPermission('branch-management/sales-report')],
        },

        {
          path: "/branch-management/stock-report",
          label: "Stock Report",
          icon: Boxes,
          roles: [hasPermission('branch-management/stock-report')],
        },
        {
          path: "/branch-management/stock-transfer",
          label: "Stock Transfer",
          icon: BaggageClaim,
          roles: [hasPermission('branch-management/stock-transfer')],
        },
        {
          path: "/branch-management/receive-stock",
          label: "Receive Stock",
          icon: Download,
          roles: [hasPermission('branch-management/receive-stock')],
        },
      ],
    },

    {
      path: "/CreateCompany",
      icon: Castle,
      label: "Company",
      roles: [hasPermission('CreateCompany')],
    },

    {
      path: "/InvoiceTemplate",
      icon: FileText,
      label: "Invoice Template",
      roles: [hasPermission('InvoiceTemplate')],
    },

    { path: "/settings", icon: SettingsIcon, label: "Settings" },

    
  ];

  // Persist collapsed state in localStorage
  useEffect(() => {
    const storedState = localStorage.getItem("isNavVisible");
    if (storedState !== null) setIsNavVisible(JSON.parse(storedState));
  }, []);

  const toggleNav = () => {
    setIsNavVisible((prev) => {
      localStorage.setItem("isNavVisible", !prev);
      return !prev;
    });
  };

  // Expand menus based on active route
  useEffect(() => {
    navItems.forEach((item) => {
      if (
        item.subItems?.some((subItem) =>
          location.pathname.startsWith(subItem.path)
        )
      ) {
        setOpenMenus((prev) => ({ ...prev, [item.path]: true }));
      }
    });
  }, [location.pathname]);

  // Handle menu toggle
  const toggleMenu = (menuPath) => {
    setOpenMenus((prev) => ({
      ...prev,
      [menuPath]: !prev[menuPath],
    }));
  };

  // Search functionality
  useEffect(() => {
    if (searchTerm) {
      const filtered = navItems.filter(
        (item) =>
          item.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.subItems?.some((subItem) =>
            subItem.label.toLowerCase().includes(searchTerm.toLowerCase())
          )
      );
      setFilteredItems(filtered);
    } else {
      setFilteredItems(navItems);
    }
  }, [searchTerm]);

  const itemsToRender = searchTerm ? filteredItems : navItems;

  if (isPosOpen) {
    return null; // Return nothing if POS is open (hide sidebar)
  }

  return (
    <div className="flex ">
      <aside
        className={`sticky ml-2 top-0 h-screen overflow-y-auto dark:bg-gray-800 transition-all duration-300 
          ${isNavVisible ? "w-64" : "w-20"}`}
      >
        <nav className="flex flex-col mt-20 h-100">
          {/* Search Bar */}
          <div className="flex items-center p-1 mt-4 border-b dark:border-gray-700">
            <button
              onClick={toggleNav}
              className="p-2 bg-gray-200 rounded-full text-slate-800 dark:bg-slate-600 hover:bg-amber-600 dark:hover:bg-amber-600"
              title={isNavVisible ? "Collapse Sidebar" : "Expand Sidebar"}
            >
              {isNavVisible ? <PanelLeftClose size={25} /> : <Menu size={25} />}
            </button>{" "}
            {isNavVisible && (
              <div className="relative flex-1">
                <input
                  type="text"
                  placeholder="Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full p-2 border rounded-lg border-amber-600 dark:border-amber-600 dark:bg-gray-700 dark:text-white"
                />
                <Search
                  size={16}
                  className="absolute text-gray-400 right-3 top-3 dark:text-gray-500"
                />
              </div>
            )}
          </div>

          {/* Navigation Items */}
          <div className="flex-1 overflow-y-auto">
            {itemsToRender
              .filter((item) => !item.roles || item.roles.includes(user?.role))
              .map((item) => {
                const Icon = item.icon;
                const isActive = location.pathname.startsWith(item.path);
                const isMenuOpen = openMenus[item.path] || false;

                return (
                  <div key={item.path}>
                    <Link
                      to={item.subItems ? "#" : item.path}
                      onClick={() => item.subItems && toggleMenu(item.path)}
                      className={`hover:bg-amber-600 dark:hover:bg-slate-600 flex items-center p-3 rounded-lg cursor-pointer transition-colors duration-100 ${isActive
                          ? " hover:text-slate-900 text-blue-700  dark:text-amber-600"
                          : "text-gray-700 hover:text-slate-900 dark:text-gray-400 dark:hover:text-cyan-500"
                        }`}
                    >
                      <Icon size={20} />
                      {isNavVisible && (
                        <span className="ml-3">{item.label}</span>
                      )}
                      {item.subItems && isNavVisible && (
                        <span className="ml-auto">
                          {isMenuOpen ? (
                            <ChevronDown size={16} />
                          ) : (
                            <ChevronRight size={16} />
                          )}
                        </span>
                      )}
                    </Link>
                    {isMenuOpen &&
                      isNavVisible &&
                      item.subItems
                        ?.filter(
                          (subItem) =>
                            !subItem.roles || subItem.roles.includes(user?.role)
                        )
                        .map((subItem) => (
                          <Link
                            key={subItem.path}
                            to={subItem.path}
                            className={`flex items-center ml-8 p-2 rounded-lg transition-colors duration-200 ${location.pathname === subItem.path
                                ? "bg-amber-500 text-blue-700 dark:bg-blue-800 dark:text-blue-300"
                                : "text-gray-700 hover:bg-amber-500 dark:text-gray-400 dark:hover:bg-gray-700"
                              }`}
                          >
                            {subItem.icon && <subItem.icon size={16} />}
                            <span className="ml-3">{subItem.label}</span>
                          </Link>
                        ))}
                  </div>
                );
              })}
          </div>
        </nav>
      </aside>
    </div>
  );
};

export default SideNav;
