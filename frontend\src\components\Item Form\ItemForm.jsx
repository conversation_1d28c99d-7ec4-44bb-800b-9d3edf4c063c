import React, { useState, useEffect, useRef } from "react";
import "./itemform.css";
import axios from "axios";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useAuth } from "../../context/NewAuthContext";

const ItemForm = ({ onSubmit, initialData, onClose, isBatchOnly = false }) => {
  const { user: authUser } = useAuth();
  // Force browser refresh by adding a comment - updated v2
  const [isCheckingName, setIsCheckingName] = useState(!initialData); // Skip name checking when editing
  const [productName, setProductName] = useState(
    initialData?.product_name || ""
  );
  const [productExists, setProductExists] = useState(false);
  const [showNameCheckResult, setShowNameCheckResult] = useState(false);
  const [itemCode, setItemCode] = useState(initialData?.item_code || "");

  // Helper function to get variant data from initialData
  const getVariantData = (field) => {
    console.log(`getVariantData called for field: ${field}`);
    console.log("initialData.variants:", initialData?.variants);
    
    if (initialData?.variants && initialData.variants.length > 0) {
      const result = initialData.variants[0][field] || "";
      console.log(`Found in variant[0].${field}:`, result);
      return result;
    }
    const result = initialData?.[field] || "";
    console.log(`Found in initialData.${field}:`, result);
    return result;
  };

  const [formData, setFormData] = useState({
    product_name: initialData?.product_name || "",
    item_code: initialData?.item_code || "",
    category: initialData?.category || "",
    supplier: initialData?.supplier || "",
    unit_type: initialData?.unit_type || "",
    store_location: getVariantData("store_location"),
    cabinet: getVariantData("cabinet"),
    row: getVariantData("row"),
    company: initialData?.company || "",
    branch_name: initialData?.branch_name || "",
    branch_qty: initialData?.branch_qty || "",
    extra_fields: (() => {
      try {
        console.log("=== EXTRA FIELDS DEBUG ===");
        console.log("initialData:", initialData);
        console.log("initialData.extra_fields:", initialData?.extra_fields);
        console.log("initialData.extra_fields type:", typeof initialData?.extra_fields);
        console.log("initialData.extra_fields isArray:", Array.isArray(initialData?.extra_fields));
        
        // First check if extra_fields exists in initialData (product level)
        if (initialData?.extra_fields) {
          console.log("Found extra_fields in initialData");
          if (typeof initialData.extra_fields === "string") {
            const parsed = JSON.parse(initialData.extra_fields);
            console.log("Parsed string extra_fields:", parsed);
            return Array.isArray(parsed) ? parsed : [];
          } else if (Array.isArray(initialData.extra_fields)) {
            console.log("Using array extra_fields from initialData:", initialData.extra_fields);
            return initialData.extra_fields;
          }
        }
        
        // If not found at product level, check variant level
        const variantData = getVariantData("extra_fields");
        console.log("Variant data for extra_fields:", variantData);
        console.log("Variant data type:", typeof variantData);
        
        if (variantData && variantData !== "") {
          if (typeof variantData === "string") {
            const parsed = JSON.parse(variantData);
            console.log("Parsed string variant extra_fields:", parsed);
            return Array.isArray(parsed) ? parsed : [];
          } else if (Array.isArray(variantData)) {
            console.log("Using array extra_fields from variant:", variantData);
            return variantData;
          }
        }
        
        console.log("No extra_fields found, returning empty array");
        return [];
      } catch (error) {
        console.error("Error parsing extra_fields:", error);
        return [];
      }
    })(),
    phone_number: initialData?.phone_number || "",
    address: initialData?.address || "",
    contact: initialData?.contact || "",
    // Add missing pricing and batch fields
    batch_number: getVariantData("batch_number"),
    barcode: getVariantData("barcode"),
    expiry_date: getVariantData("expiry_date"),
    mrp: getVariantData("mrp"),
    buying_cost: getVariantData("buying_cost"),
    sales_price: getVariantData("sales_price"),
    wholesale_price: getVariantData("wholesale_price"),
    minimum_price: getVariantData("minimum_price"),
    minimum_stock_quantity: getVariantData("minimum_stock_quantity"),
    opening_stock_quantity: getVariantData("opening_stock_quantity"),
    opening_stock_value: getVariantData("opening_stock_value"),
  });

  const [showBatchConfirm, setShowBatchConfirm] = useState(false);
  const [pendingBatchData, setPendingBatchData] = useState(null);
  const [showProductConfirm, setShowProductConfirm] = useState(false);
  const [selectedProductForBatch, setSelectedProductForBatch] = useState(null);
  const [salesPricePercentage, setSalesPricePercentage] = useState("");
  const [wholesalePricePercentage, setWholesalePricePercentage] = useState("");
  const [minimumPricePercentage, setMinimumPricePercentage] = useState("");
  const [categories, setCategories] = useState([]);
  const [unitTypes, setUnitTypes] = useState([]);
  const [suppliers, setSuppliers] = useState([]);
  const [stores, setStores] = useState([]);
  const [companies, setCompanies] = useState([]);
  const [showAddCategory, setShowAddCategory] = useState(false);
  const [newCategory, setNewCategory] = useState("");
  const [showAddSupplier, setShowAddSupplier] = useState(false);
  const [newSupplier, setNewSupplier] = useState("");
  const [showAddUnitType, setShowAddUnitType] = useState(false);
  const [newUnitType, setNewUnitType] = useState("");
  const [showAddStore, setShowAddStore] = useState(false);
  const [newStore, setNewStore] = useState("");
  const [showAddCompany, setShowAddCompany] = useState(false);
  const [newCompanyData, setNewCompanyData] = useState({
    company_name: "",
    contact: "",
    address: "",
    opening_balance: "",
  });
  const [showCloseConfirm, setShowCloseConfirm] = useState(false);
  const [existingVariants, setExistingVariants] = useState([]);
  const [editingVariantIndex, setEditingVariantIndex] = useState(-1);
  const [showVariantsList, setShowVariantsList] = useState(false);
  const [editingVariantData, setEditingVariantData] = useState({});
  const [batchValidationError, setBatchValidationError] = useState("");
  const [productNameError, setProductNameError] = useState("");
  const [itemCodeError, setItemCodeError] = useState("");
  const [showCustomFieldForm, setShowCustomFieldForm] = useState(false);
  const [newCustomField, setNewCustomField] = useState([{ name: '', value: '' }]);
  const [editingExtraFieldIndex, setEditingExtraFieldIndex] = useState(-1);
  const [editingExtraFieldData, setEditingExtraFieldData] = useState({ name: '', value: '' });

  // Separate state for batch creation when adding new batches
  const [batchFormData, setBatchFormData] = useState({
    batch_number: "",
    expiry_date: "",
    buying_cost: "",
    sales_price: "",
    minimum_price: "",
    wholesale_price: "",
    barcode: "",
    mrp: "",
    minimum_stock_quantity: "",
    opening_stock_quantity: "",
    opening_stock_value: "",
    store_location: "",
    cabinet: "",
    row: "",
  });

  // Refs for Enter key navigation
  const productNameRef = useRef(null);
  const itemCodeRef = useRef(null);
  const categoryRef = useRef(null);
  const supplierRef = useRef(null);
  const unitTypeRef = useRef(null);
  const companyRef = useRef(null);
  const batchNumberRef = useRef(null);
  const barcodeRef = useRef(null);
  const expiryDateRef = useRef(null);
  const buyingCostRef = useRef(null);
  const salesPriceRef = useRef(null);
  const salesPricePercentageRef = useRef(null);
  const wholesalePriceRef = useRef(null);
  const wholesalePricePercentageRef = useRef(null);
  const minimumPriceRef = useRef(null);
  const minimumPricePercentageRef = useRef(null);
  const mrpRef = useRef(null);
  const minimumStockRef = useRef(null);
  const openingStockRef = useRef(null);
  const openingStockValueRef = useRef(null);
  const storeLocationRef = useRef(null);
  const cabinetRef = useRef(null);
  const rowRef = useRef(null);
  const submitButtonRef = useRef(null);

  // Refs for dialog navigation
  const productNameInputRef = useRef(null);
  const checkButtonRef = useRef(null);
  const proceedButtonRef = useRef(null);

  // Add state for barcode variant search
  const [barcodeVariants, setBarcodeVariants] = useState([]);
  const [showBarcodeVariants, setShowBarcodeVariants] = useState(false);

  // Function to fetch all variants by barcode
  const fetchVariantsByBarcode = async (barcode) => {
    try {
      const response = await axios.get('/api/product-variants/search-by-barcode', { params: { barcode } });
      setBarcodeVariants(response.data);
      setShowBarcodeVariants(true);
    } catch (error) {
      setBarcodeVariants([]);
      setShowBarcodeVariants(false);
      toast.error("No variants found for this barcode.");
    }
  };

  const checkProductName = async (name) => {
    if (!name.trim()) {
      toast.error("Product name cannot be empty");
      return;
    }
    try {
      const trimmedName = name.trim();
      console.log("Checking product name:", trimmedName);

      // Debug: Fetch all products to see what's in the database
      const allProductsResponse = await axios.get(
        "http://127.0.0.1:8000/api/products"
      );
      const allProductNames = allProductsResponse.data.data.map(
        (p) => p.product_name
      );
      console.log("All products in database:", allProductNames);
      console.log("Searching for exact match:", trimmedName);

      const response = await axios.get(
        "http://127.0.0.1:8000/api/products/check-names",
        {
          params: { names: [trimmedName] },
        }
      );

      console.log("Check names response:", response.data);
      console.log("Existing products found:", response.data.existing);

      // Case-insensitive and space-insensitive comparison to handle "hovite sl", "hovitesl", "HOVITE SL" as same product
      const normalizeForComparison = (name) => {
        return name.toLowerCase().replace(/\s+/g, "").trim();
      };

      const normalizedInput = normalizeForComparison(trimmedName);
      const exists = response.data.existing.some(
        (existingName) =>
          normalizeForComparison(existingName) === normalizedInput
      );
      console.log("Product exists (case and space insensitive):", exists);

      // If case and space insensitive match found, show the actual database name for consistency
      if (exists) {
        const actualProductName = response.data.existing.find(
          (existingName) =>
            normalizeForComparison(existingName) === normalizedInput
        );
        if (actualProductName && actualProductName !== trimmedName) {
          console.log(
            `Using database name: "${actualProductName}" instead of "${trimmedName}"`
          );
          setProductName(actualProductName); // Use the actual database name
          setFormData((prev) => ({ ...prev, product_name: actualProductName }));
        }
      }

      setProductExists(exists);
      setShowNameCheckResult(true);
      // Only set formData if we haven't already set it with the actual database name
      if (
        !exists ||
        !response.data.existing.find(
          (existingName) =>
            existingName.toLowerCase().trim() ===
              trimmedName.toLowerCase().trim() && existingName !== trimmedName
        )
      ) {
        setFormData((prev) => ({ ...prev, product_name: trimmedName }));
      }
    } catch (error) {
      console.error("Error checking product name:", error);
      toast.error("Failed to check product name");
    }
  };

  const handleNameSubmit = (e) => {
    e.preventDefault();
    checkProductName(productName);
  };

  const handleNameCheckConfirm = () => {
    setShowNameCheckResult(false);
    setIsCheckingName(false);
    // Ensure product name is preserved in formData when switching to batch mode
    if (productExists && productName) {
      setFormData((prev) => ({ ...prev, product_name: productName }));
      console.log("Product name preserved for batch mode:", productName);
    }
  };

  const handleNameCheckCancel = () => {
    setShowNameCheckResult(false);
    resetToNameInput();
  };

  const resetToNameInput = () => {
    setIsCheckingName(true);
    setProductExists(false);
    setShowNameCheckResult(false);
    setProductName("");
    setFormData({
      product_name: "",
      item_code: "",
      category: "",
      supplier: "",
      unit_type: "",
      store_location: "",
      cabinet: "",
      row: "",
      company: "",
      branch_name: "",
      branch_qty: "",
      extra_fields: [],
      phone_number: "",
      address: "",
      contact: "",
    });
    // Clear all validation errors
    setBatchValidationError("");
    setProductNameError("");
    setItemCodeError("");
  };

  const resetFormData = () => {
    setFormData({
      product_name: "",
      item_code: "",
      category: "",
      supplier: "",
      unit_type: "",
      store_location: "",
      cabinet: "",
      row: "",
      company: "",
      branch_name: "",
      branch_qty: "",
      extra_fields: [],
      phone_number: "",
      address: "",
      contact: "",
    });
  };

  const handleConfirmBatch = async () => {
    if (pendingBatchData) {
      console.log("handleConfirmBatch - pendingBatchData:", pendingBatchData);
      console.log(
        "handleConfirmBatch - product_name:",
        pendingBatchData.product_name
      );

      try {
        let productId = pendingBatchData.product_id;
        let selectedProduct = null;

        if (!productId) {
          const productResponse = await axios.get(
            "http://127.0.0.1:8000/api/products",
            {
              params: { product_name: pendingBatchData.product_name },
            }
          );

          if (!productResponse.data.data.length) {
            toast.error("Product not found");
            return;
          }

          // Find exact match first
          const exactMatch = productResponse.data.data.find(
            (product) => product.product_name === pendingBatchData.product_name
          );

          if (exactMatch) {
            selectedProduct = exactMatch;
            productId = exactMatch.product_id;
          } else {
            // If no exact match, show error instead of picking first result
            toast.error(
              `No exact match found for product "${pendingBatchData.product_name}". Please check the product name.`
            );
            return;
          }
        } else {
          // Verify the product exists with the given ID
          try {
            const productResponse = await axios.get(
              `http://127.0.0.1:8000/api/products/${productId}`
            );
            selectedProduct = productResponse.data.data;
          } catch (error) {
            toast.error("Product not found with the given ID");
            return;
          }
        }

        // Additional validation: confirm product name matches if both are available
        if (
          selectedProduct &&
          pendingBatchData.product_name &&
          selectedProduct.product_name !== pendingBatchData.product_name
        ) {
          toast.error(
            `Product name mismatch. Expected: "${selectedProduct.product_name}", Got: "${pendingBatchData.product_name}"`
          );
          return;
        }

        // Show product confirmation dialog before proceeding
        setSelectedProductForBatch(selectedProduct);
        setShowProductConfirm(true);
        setShowBatchConfirm(false);
      } catch (error) {
        console.error("Error validating product:", error);
        toast.error("Error validating product. Please try again.");
      }
    }
  };

  const handleConfirmProductForBatch = async () => {
    if (!selectedProductForBatch || !pendingBatchData) {
      toast.error("Missing product or batch data");
      return;
    }

    try {
      const productId = selectedProductForBatch.product_id;

      const {
        batch_number,
        expiry_date,
        buying_cost,
        sales_price,
        minimum_price,
        wholesale_price,
        barcode,
        mrp,
        minimum_stock_quantity,
        opening_stock_quantity,
        opening_stock_value,
        store_location,
        cabinet,
        row,
        extra_fields,
      } = pendingBatchData;

      if (
        sales_price === undefined ||
        sales_price === null ||
        isNaN(sales_price) ||
        sales_price <= 0
      ) {
        toast.error("Sales Price is required and must be a positive number");
        return;
      }
      if (mrp === undefined || mrp === null || isNaN(mrp) || mrp <= 0) {
        toast.error("MRP is required and must be a positive number");
        return;
      }

      const batchData = {
        batch_number,
        expiry_date,
        buying_cost,
        sales_price,
        minimum_price,
        wholesale_price,
        barcode,
        mrp,
        minimum_stock_quantity,
        opening_stock_quantity,
        opening_stock_value,
        store_location,
        cabinet,
        row,
        extra_fields,
      };

      const response = await axios.post(
        `http://127.0.0.1:8000/api/products/${productId}/addBatch`,
        batchData
      );

      if (response.status === 200) {
        toast.success("Batch updated successfully");
      } else if (response.status === 201) {
        toast.success("Batch added successfully");
      } else {
        toast.info(response.data.message || "Batch operation completed");
      }

      // Refresh variants list to show the newly added batch
      if (productId) {
        await refreshVariants(productId);
      }

      resetToNameInput();
      setShowProductConfirm(false);
      setPendingBatchData(null);
      setSelectedProductForBatch(null);
      onClose();
    } catch (error) {
      console.error("Error adding batch:", error);
      if (error.response?.data?.errors) {
        Object.values(error.response.data.errors).forEach((err) =>
          toast.error(err[0])
        );
      } else {
        toast.error(error.response?.data?.message || "Failed to add batch");
      }
    }
  };

  const handleCancelBatch = () => {
    setShowBatchConfirm(false);
    setPendingBatchData(null);
  };

  const handleCancelProductConfirm = () => {
    setShowProductConfirm(false);
    setSelectedProductForBatch(null);
    setPendingBatchData(null);
  };

  const handleEditVariant = (index) => {
    console.log('[DEBUG] handleEditVariant called for index:', index, 'variant:', existingVariants[index]);
    console.log('[DEBUG] handleEditVariant - existingVariants length:', existingVariants.length);
    console.log('[DEBUG] handleEditVariant - setting editingVariantIndex to:', index);
    console.log('[DEBUG] handleEditVariant - current editingVariantIndex before setState:', editingVariantIndex);
    
    // Format expiry_date for <input type="date" />
    const formatDateForInput = (dateString) => {
      if (!dateString) return "";
      return dateString.split("T")[0];
    };
    
    const variantData = {
      ...existingVariants[index],
      expiry_date: formatDateForInput(existingVariants[index].expiry_date),
    };
    
    console.log('[DEBUG] handleEditVariant - formatted variant data:', variantData);
    
    setEditingVariantIndex(index);
    setEditingVariantData(variantData);
    
    console.log('[DEBUG] handleEditVariant - editingVariantIndex set to:', index);
    console.log('[DEBUG] handleEditVariant - editingVariantData set to:', variantData);
  };

  const handleUpdateVariant = async () => {
    if (editingVariantIndex === -1) return;

    // Build update data, only including fields that have values
    const updateData = {};
    
    // Helper function to add field only if it has a value
    const addFieldIfValue = (field, value) => {
      if (value !== null && value !== undefined && value !== "") {
        updateData[field] = value;
      }
    };
    
    // Add fields only if they have values
    addFieldIfValue('batch_number', editingVariantData.batch_number);
    addFieldIfValue('expiry_date', editingVariantData.expiry_date);
    addFieldIfValue('buying_cost', editingVariantData.buying_cost ? parseFloat(editingVariantData.buying_cost) : null);
    addFieldIfValue('sales_price', editingVariantData.sales_price ? parseFloat(editingVariantData.sales_price) : null);
    addFieldIfValue('minimum_price', editingVariantData.minimum_price ? parseFloat(editingVariantData.minimum_price) : null);
    addFieldIfValue('wholesale_price', editingVariantData.wholesale_price ? parseFloat(editingVariantData.wholesale_price) : null);
    addFieldIfValue('mrp', editingVariantData.mrp ? parseFloat(editingVariantData.mrp) : null);
    addFieldIfValue('minimum_stock_quantity', editingVariantData.minimum_stock_quantity ? parseFloat(editingVariantData.minimum_stock_quantity) : null);
    addFieldIfValue('opening_stock_quantity', editingVariantData.opening_stock_quantity ? parseFloat(editingVariantData.opening_stock_quantity) : null);
    addFieldIfValue('opening_stock_value', editingVariantData.opening_stock_value ? parseFloat(editingVariantData.opening_stock_value) : null);
    addFieldIfValue('store_location', editingVariantData.store_location);
    addFieldIfValue('cabinet', editingVariantData.cabinet);
    addFieldIfValue('row', editingVariantData.row);
    
    // Handle barcode carefully - only update if it's different from the current value
    const currentVariant = existingVariants[editingVariantIndex];
    if (editingVariantData.barcode && editingVariantData.barcode !== currentVariant.barcode) {
      addFieldIfValue('barcode', editingVariantData.barcode);
    }

    // --- EVEN MORE DETAILED LOGGING ---
    console.log("[DEBUG][handleUpdateVariant] index:", editingVariantIndex);
    console.log("[DEBUG][handleUpdateVariant] currentVariant:", currentVariant);
    console.log("[DEBUG][handleUpdateVariant] editingVariantData:", editingVariantData);
    console.log("[DEBUG][handleUpdateVariant] updateData:", updateData);
    console.log("[DEBUG][handleUpdateVariant] existingVariants:", existingVariants);

    // Validate required fields
    if (!updateData.sales_price || updateData.sales_price <= 0) {
      toast.error("Sales Price is required and must be a positive number");
      return;
    }
    
    if (!updateData.mrp || updateData.mrp <= 0) {
      toast.error("MRP is required and must be a positive number");
      return;
    }

    try {
      const variant = existingVariants[editingVariantIndex];
      // Retrieve token from localStorage
      const user = JSON.parse(localStorage.getItem("user"));
      const token = user?.token;

      // --- EVEN MORE DETAILED LOGGING ---
      console.log("[DEBUG][handleUpdateVariant] Sending PUT request to:", `http://127.0.0.1:8000/api/product-variants/${variant.product_variant_id}`);
      console.log("[DEBUG][handleUpdateVariant] Payload:", updateData);
      console.log("[DEBUG][handleUpdateVariant] Variant ID:", variant.product_variant_id);
      console.log("[DEBUG][handleUpdateVariant] Product ID:", variant.product_id);
      console.log("[DEBUG][handleUpdateVariant] Current batch number:", variant.batch_number);
      console.log("[DEBUG][handleUpdateVariant] New batch number:", updateData.batch_number);

      const response = await axios.put(
        `http://127.0.0.1:8000/api/product-variants/${variant.product_variant_id}`,
        updateData,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      // --- EVEN MORE DETAILED LOGGING ---
      console.log("[DEBUG][handleUpdateVariant] Update response:", response.data);

      if (response.status === 200) {
        // Update the local state with the new values immediately
        const updatedVariants = [...existingVariants];
        updatedVariants[editingVariantIndex] = {
          ...updatedVariants[editingVariantIndex],
          ...updateData,
          // Ensure the product_variant_id is preserved
          product_variant_id: variant.product_variant_id
        };
        setExistingVariants(updatedVariants);
        
        console.log("[DEBUG][handleUpdateVariant] Updated variant in local state:", updatedVariants[editingVariantIndex]);
        
        toast.success("Variant updated successfully");
        
        // Reset editing state
        setEditingVariantIndex(-1);
        setEditingVariantData({});
        
        // Prevent any further form submission by stopping the event propagation
        console.log("[DEBUG][handleUpdateVariant] Variant update completed, resetting editing state");
      }
    } catch (error) {
      // --- EVEN MORE DETAILED LOGGING ---
      console.error("[DEBUG][handleUpdateVariant] Error updating variant:", error);
      console.error("[DEBUG][handleUpdateVariant] Error response data:", error.response?.data);
      console.error("[DEBUG][handleUpdateVariant] Error response status:", error.response?.status);
      console.error("[DEBUG][handleUpdateVariant] Request data sent:", updateData);
      
      // Improved user feedback for unique constraint violation
      if (error.response?.data?.message) {
        toast.error(error.response.data.message);
      } else if (error.response?.data?.error) {
        toast.error(error.response.data.error);
      } else if (error.response?.status === 422) {
        toast.error("Batch number already exists for this product. Please use a unique batch number.");
      } else {
        toast.error("Failed to update variant");
      }
    }
  };

  const handleVariantDataChange = (field, value) => {
    setEditingVariantData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleCancelVariantEdit = () => {
    setEditingVariantIndex(-1);
    setEditingVariantData({});
  };

  const handleDeleteVariant = async (index) => {
    if (
      !window.confirm("Are you sure you want to delete this batch/variant?")
    ) {
      return;
    }

    try {
      const variant = existingVariants[index];
      // Try to get user ID from context, then localStorage, then sessionStorage
      let userId = authUser?.id;
      let token = authUser?.token;
      if (!userId || !token) {
        let user = null;
        if (localStorage.getItem("user")) {
          user = JSON.parse(localStorage.getItem("user"));
        } else if (sessionStorage.getItem("user")) {
          user = JSON.parse(sessionStorage.getItem("user"));
        }
        userId = user?.id;
        token = user?.token;
      }
      if (!userId) {
        toast.error("No user ID found. Please login again.");
        return;
      }
      const response = await axios.delete(
        `http://127.0.0.1:8000/api/product-variants/${variant.product_variant_id}`,
        {
          headers: { Authorization: `Bearer ${token}` },
          data: { deleted_by: userId },
        }
      );

      if (response.status === 200) {
        // Handle different types of deletion responses
        if (response.data.soft_deleted) {
          toast.success(response.data.message);
        } else {
          toast.success("Variant deleted successfully");
        }
        
        const updatedVariants = existingVariants.filter((_, i) => i !== index);
        setExistingVariants(updatedVariants);

        // Reset editing if we were editing the deleted variant
        if (editingVariantIndex === index) {
          setEditingVariantIndex(-1);
          setEditingVariantData({});
        } else if (editingVariantIndex > index) {
          // Adjust editing index if we deleted a variant before the one being edited
          setEditingVariantIndex(editingVariantIndex - 1);
        }
      }
    } catch (error) {
      console.error("Error deleting variant:", error);
      toast.error(
        error.response?.data?.message || "Failed to delete variant"
      );
    }
  };

  const handleAddCategory = async () => {
    if (!newCategory.trim()) {
      toast.error("Category name cannot be empty");
      return;
    }
    try {
      const response = await axios.post(
        "http://127.0.0.1:8000/api/categories",
        {
          name: newCategory.trim(),
        }
      );
      setCategories((prev) => [...prev, response.data]);
      setFormData((prev) => ({ ...prev, category: response.data.name }));
      setNewCategory("");
      setShowAddCategory(false);
      toast.success("Category added successfully");
    } catch (error) {
      console.error("Error adding category:", error);
      toast.error("Failed to add category");
    }
  };

  const handleAddStore = async () => {
    if (!newStore.trim()) {
      toast.error("Store location cannot be empty");
      return;
    }
    try {
      const response = await axios.post(
        "http://127.0.0.1:8000/api/store-locations",
        {
          store_name: newStore.trim(),
          phone_number: formData.phone_number || "",
          address: formData.address || "",
        }
      );
      setStores((prev) => [...prev, response.data]);
      setFormData((prev) => ({
        ...prev,
        store_location: response.data.store_name,
        phone_number: response.data.phone_number || "",
        address: response.data.address || "",
      }));
      setNewStore("");
      setShowAddStore(false);
      toast.success("Store location added successfully");
    } catch (error) {
      console.error("Error adding store location:", error);
      toast.error("Failed to add store location");
    }
  };

  const handleAddSupplier = async () => {
    if (!newSupplier.trim()) {
      toast.error("Supplier name cannot be empty");
      return;
    }
    try {
      const response = await axios.post("http://127.0.0.1:8000/api/suppliers", {
        supplier_name: newSupplier.trim(),
        contact: formData.contact || "",
        address: formData.address || "",
      });
      setSuppliers((prev) => [...prev, response.data]);
      setFormData((prev) => ({
        ...prev,
        supplier: response.data.supplier_name,
        contact: response.data.contact || "",
        address: response.data.address || "",
      }));
      setNewSupplier("");
      setShowAddSupplier(false);
      toast.success("Supplier added successfully");
    } catch (error) {
      console.error("Error adding supplier:", error);
      toast.error("Failed to add supplier");
    }
  };

  const handleAddUnitType = async () => {
    if (!newUnitType.trim()) {
      toast.error("Unit type name cannot be empty");
      return;
    }
    try {
      const response = await axios.post("http://127.0.0.1:8000/api/units", {
        unit_name: newUnitType.trim(),
      });
      setUnitTypes((prev) => [...prev, response.data]);
      setFormData((prev) => ({ ...prev, unit_type: response.data.unit_name }));
      setNewUnitType("");
      setShowAddUnitType(false);
      toast.success("Unit type added successfully");
    } catch (error) {
      console.error("Error adding unit type:", error);
      toast.error("Failed to add unit type");
    }
  };

  const handleAddCompany = async () => {
    if (!newCompanyData.company_name.trim()) {
      toast.error("Company name cannot be empty");
      return;
    }
    try {
      const response = await axios.post("http://127.0.0.1:8000/api/companies", {
        company_name: newCompanyData.company_name.trim(),
        contact: newCompanyData.contact.trim() || null,
        address: newCompanyData.address.trim() || null,
        opening_balance: newCompanyData.opening_balance
          ? parseFloat(newCompanyData.opening_balance)
          : null,
      });
      setCompanies((prev) => [...prev, response.data]);
      setFormData((prev) => ({ ...prev, company: response.data.company_name }));
      setNewCompanyData({
        company_name: "",
        contact: "",
        address: "",
        opening_balance: "",
      });
      setShowAddCompany(false);
      toast.success("Company added successfully");
    } catch (error) {
      console.error("Error adding company:", error);
      toast.error("Failed to add company");
    }
  };

  useEffect(() => {
    const fetchCompanies = async () => {
      try {
        const response = await axios.get("http://127.0.0.1:8000/api/companies");
        setCompanies(response.data);
      } catch (error) {
        console.error("Error fetching companies:", error);
      }
    };

    fetchCompanies();
  }, []);

  // Function to refresh variants from the server
  const refreshVariants = async (productId) => {
    try {
      const response = await axios.get(
        `http://127.0.0.1:8000/api/products/${productId}`
      );
      if (response.data.data && response.data.data.variants) {
        setExistingVariants(response.data.data.variants);
        setShowVariantsList(true);
      }
    } catch (error) {
      console.error("Error refreshing variants:", error);
    }
  };

  useEffect(() => {
    // Initialize existing variants if editing a product
    if (initialData?.variants && initialData.variants.length > 0) {
      setExistingVariants(initialData.variants);
      setShowVariantsList(true);
    }

    // Debug logging for batch-only mode
    console.log("ItemForm useEffect - Debug info:", {
      isBatchOnly,
      initialData: initialData ? 'exists' : 'null',
      productExists,
      hasInitialData: !!initialData
    });

    // Auto-fill product name and generate batch number for batch-only mode
    if (isBatchOnly && initialData) {
      // Pre-fill the product name from the existing product
      setFormData((prev) => ({
        ...prev,
        product_name: initialData.product_name || "",
        item_code: initialData.item_code || "", // Auto-fill item code
        category: initialData.category || "",
        supplier: initialData.supplier || "",
        unit_type: initialData.unit_type || "",
        company: initialData.company || "",
        branch_name: initialData.branch_name || "",
        branch_qty: initialData.branch_qty || "",
        store_location: initialData.store_location || "",
        cabinet: initialData.cabinet || "",
        row: initialData.row || "",
      }));
      setProductName(initialData.product_name || "");
      setItemCode(initialData.item_code ||"");
      
      // Auto-generate a batch number if not provided
      if (!batchFormData.batch_number) {
        const generateBatchNumber = () => {
          const timestamp = Date.now().toString().slice(-6);
          const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
          return `BATCH${timestamp}${random}`;
        };
        
        setBatchFormData((prev) => ({
          ...prev,
          batch_number: generateBatchNumber(),
        }));
      }
    }

    const fetchData = async () => {
      try {
        const [categoriesRes, unitTypesRes, suppliersRes, storesRes] =
          await Promise.all([
            axios.get("http://127.0.0.1:8000/api/categories"),
            axios.get("http://127.0.0.1:8000/api/units"),
            axios.get("http://127.0.0.1:8000/api/suppliers"),
            axios.get("http://127.0.0.1:8000/api/store-locations"),
          ]);

        setCategories(categoriesRes.data);
        setUnitTypes(unitTypesRes.data);
        setSuppliers(suppliersRes.data);
        setStores(storesRes.data);
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("Error fetching form data: " + error.message);
      }
    };

    fetchData();

    // Auto-focus on product name field when form loads
    setTimeout(() => {
      if (productNameRef.current) {
        productNameRef.current.focus();
      }
    }, 100);
  }, []);

  // Monitor editingVariantIndex changes
  useEffect(() => {
    console.log("[DEBUG] editingVariantIndex changed to:", editingVariantIndex);
    if (editingVariantIndex !== -1) {
      console.log("[DEBUG] Now editing variant at index:", editingVariantIndex);
      console.log("[DEBUG] Variant data:", existingVariants[editingVariantIndex]);
    }
  }, [editingVariantIndex, existingVariants]);





  // Note: Removed auto-calculate useEffect hooks that were causing cursor jumping
  // Price calculations are now handled only in handleBatchChange when buying_cost changes
  // This prevents interference with user input in price fields



  const handleChange = (e) => {
    let { name, value } = e.target;

    const parseOrZero = (val) => {
      const parsed = parseFloat(val);
      return isNaN(parsed) ? 0 : parsed;
    };

    setFormData((prevData) => ({ ...prevData, [name]: value }));

    // Only auto-calculate if percentages are set and buying cost changes
    if (name === "buying_cost") {
      const buyingCost = parseOrZero(value);
      const updatedData = {};

      // Only calculate if percentage is set (not zero)
      if (salesPricePercentage && parseFloat(salesPricePercentage) > 0) {
        updatedData.sales_price = (
          buyingCost +
          buyingCost * (parseFloat(salesPricePercentage) / 100)
        ).toFixed(2);
      }

      if (
        wholesalePricePercentage &&
        parseFloat(wholesalePricePercentage) > 0
      ) {
        updatedData.wholesale_price = (
          buyingCost +
          buyingCost * (parseFloat(wholesalePricePercentage) / 100)
        ).toFixed(2);
      }

      if (minimumPricePercentage && parseFloat(minimumPricePercentage) > 0) {
        updatedData.minimum_price = (
          buyingCost +
          buyingCost * (parseFloat(minimumPricePercentage) / 100)
        ).toFixed(2);
      }

      if (Object.keys(updatedData).length > 0) {
        setFormData((prevData) => ({ ...prevData, ...updatedData }));
      }
    } else if (name === "sales_price") {
      const buyingCost = parseOrZero(
        productExists || isBatchOnly
          ? batchFormData.buying_cost
          : formData.buying_cost
      );
      const salesPrice = parseOrZero(value);
      const percentage =
        buyingCost === 0 ? 0 : ((salesPrice - buyingCost) / buyingCost) * 100;
      setSalesPricePercentage(percentage.toFixed(2));
    } else if (name === "wholesale_price") {
      const buyingCost = parseOrZero(
        productExists || isBatchOnly
          ? batchFormData.buying_cost
          : formData.buying_cost
      );
      const wholesalePrice = parseOrZero(value);
      const percentage =
        buyingCost === 0
          ? 0
          : ((wholesalePrice - buyingCost) / buyingCost) * 100;
      setWholesalePricePercentage(percentage.toFixed(2));
    } else if (name === "minimum_price") {
      const buyingCost = parseOrZero(
        productExists || isBatchOnly
          ? batchFormData.buying_cost
          : formData.buying_cost
      );
      const minimumPrice = parseOrZero(value);
      const percentage =
        buyingCost === 0 ? 0 : ((minimumPrice - buyingCost) / buyingCost) * 100;
      setMinimumPricePercentage(percentage.toFixed(2));
    }

    // Calculate opening stock value (only when not directly editing opening_stock_value)
    if (
      (name === "opening_stock_quantity" || name === "buying_cost") &&
      name !== "opening_stock_value"
    ) {
      const quantity =
        name === "opening_stock_quantity"
          ? parseOrZero(value)
          : parseOrZero(formData.opening_stock_quantity);
      const cost =
        name === "buying_cost"
          ? parseOrZero(value)
          : parseOrZero(formData.buying_cost);
      const opening_stock_value = quantity * cost;

      // Use setTimeout to avoid conflicting with the main state update
      setTimeout(() => {
        setFormData((prevData) => ({
          ...prevData,
          opening_stock_value: opening_stock_value.toFixed(2),
        }));
      }, 0);
    }
  };

  const handleExtraFieldChange = (index, e) => {
    const { name, value } = e.target;
    const updatedExtraFields = [...formData.extra_fields];
    updatedExtraFields[index][name] = value;
    setFormData({ ...formData, extra_fields: updatedExtraFields });
  };

  const addExtraField = () => {
    setShowCustomFieldForm(true);
  };

  const handleCustomFieldSubmit = () => {
    if (!newCustomField.name.trim()) {
      toast.error("Field name is required");
      return;
    }
    
    setFormData((prevData) => ({
      ...prevData,
      extra_fields: [...prevData.extra_fields, { 
        name: newCustomField.name.trim(), 
        value: newCustomField.value.trim() 
      }],
    }));
    
    setNewCustomField({ name: "", value: "" });
    setShowCustomFieldForm(false);
    toast.success("Custom field added successfully");
  };

  const handleCustomFieldCancel = () => {
    setNewCustomField({ name: "", value: "" });
    setShowCustomFieldForm(false);
  };

  const removeExtraField = (index) => {
    const updatedExtraFields = formData.extra_fields.filter(
      (_, i) => i !== index
    );
    setFormData({ ...formData, extra_fields: updatedExtraFields });
  };

  const handleEditExtraField = (index) => {
    setEditingExtraFieldIndex(index);
    setEditingExtraFieldData({
      name: formData.extra_fields[index].name,
      value: formData.extra_fields[index].value
    });
  };

  const handleUpdateExtraField = () => {
    if (editingExtraFieldIndex === -1) return;

    if (!editingExtraFieldData.name.trim()) {
      toast.error("Field name is required");
      return;
    }

    const updatedExtraFields = [...formData.extra_fields];
    updatedExtraFields[editingExtraFieldIndex] = {
      name: editingExtraFieldData.name.trim(),
      value: editingExtraFieldData.value.trim()
    };

    setFormData({ ...formData, extra_fields: updatedExtraFields });
    setEditingExtraFieldIndex(-1);
    setEditingExtraFieldData({ name: '', value: '' });
    toast.success("Custom field updated successfully");
  };

  const handleCancelExtraFieldEdit = () => {
    setEditingExtraFieldIndex(-1);
    setEditingExtraFieldData({ name: '', value: '' });
  };

  const handleExtraFieldDataChange = (field, value) => {
    setEditingExtraFieldData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    console.log("[DEBUG][handleSubmit] Form submission started");
    console.log("[DEBUG][handleSubmit] Current editingVariantIndex:", editingVariantIndex);

    // If we're editing a variant, don't proceed with form submission
    if (editingVariantIndex !== -1) {
      console.log("[DEBUG][handleSubmit] Form submission blocked - currently editing variant");
      return;
    }

    // Additional check: if we just completed a variant update, don't proceed
    // This prevents race conditions where form submission happens after variant update
    if (editingVariantData && Object.keys(editingVariantData).length > 0) {
      console.log("[DEBUG][handleSubmit] Form submission blocked - variant data still present");
      return;
    }

    // Check for validation errors before submission
    if (productNameError) {
      toast.error("Please fix the product name error before submitting");
      return;
    }

    if (itemCodeError) {
      toast.error("Please fix the item code error before submitting");
      return;
    }

    if (batchValidationError) {
      toast.error("Please fix the batch number error before submitting");
      return;
    }

    // Determine which form data to use
    const currentFormData =
      productExists || isBatchOnly ? batchFormData : formData;
    const isNewProduct = !initialData && !(productExists || isBatchOnly); // This is correct - new product only when no initialData and not in batch mode

    // Validation for new products
    if (isNewProduct && !formData.product_name.trim()) {
      toast.error("Product Name is required");
      return;
    }

    // Only validate variant fields if we're in batch mode or creating a new product
    // NOT when editing an existing product (to prevent validation of stale variant data)
    if (productExists || isBatchOnly || isNewProduct) {
      // Sales price validation - check the correct form data
      const salesPrice = parseFloat(currentFormData.sales_price) || 0;
      if (!salesPrice || salesPrice <= 0) {
        toast.error("Sales Price must be a positive number");
        return;
      }

      // MRP validation - check the correct form data
      const mrp = parseFloat(currentFormData.mrp) || 0;
      if (!mrp || mrp <= 0) {
        toast.error("MRP must be a positive number");
        return;
      }

      // Batch number validation
      if (currentFormData.batch_number && !currentFormData.batch_number.trim()) {
        toast.error("Batch Number cannot be empty if provided");
        return;
      }
    }

    // Check for duplicate batch validation error
    if (batchValidationError) {
      toast.error(batchValidationError);
      return;
    }

    // Debug logging for currentFormData
    console.log("[DEBUG] currentFormData contents:", currentFormData);
    console.log("[DEBUG] formData contents:", formData);

    // Build item data based on form type
    const itemData = {
      // Always include product_name if available (for batch operations)
      product_name: formData.product_name || productName || null,

      // For new products OR editing products, include product details
      ...((isNewProduct || initialData) && {
        item_code: formData.item_code || null,
        category: formData.category || null,
        supplier: formData.supplier || null,
        unit_type: formData.unit_type || null,
        company: formData.company || null,
        branch_name: formData.branch_name || null,
        branch_qty: parseFloat(formData.branch_qty) || 0,
        extra_fields: JSON.stringify(formData.extra_fields || []),
      }),

      // Include product ID if editing
      ...(initialData?.product_id
        ? { product_id: initialData.product_id }
        : {}),

      // Include editingVariantIndex flag if we're editing a specific variant
      ...(editingVariantIndex !== -1
        ? { editingVariantIndex: editingVariantIndex }
        : {}),
    };

    // Only include variant fields if we're in batch mode or creating a new product
    // NOT when editing an existing product (to prevent double updates)
    if (productExists || isBatchOnly || isNewProduct) {
      console.log("[DEBUG] Including variant fields in form data (batch mode or new product)");
      Object.assign(itemData, {
        batch_number: currentFormData.batch_number || null,
        expiry_date: currentFormData.expiry_date || null,
        buying_cost: parseFloat(currentFormData.buying_cost) || 0,
        sales_price: parseFloat(currentFormData.sales_price) || 0,
        minimum_price: parseFloat(currentFormData.minimum_price) || 0,
        wholesale_price: parseFloat(currentFormData.wholesale_price) || 0,
        barcode: currentFormData.barcode || null,
        mrp: parseFloat(currentFormData.mrp) || 0,
        minimum_stock_quantity:
          parseFloat(currentFormData.minimum_stock_quantity) || 0,
        opening_stock_quantity:
          parseFloat(currentFormData.opening_stock_quantity) || 0,
        opening_stock_value: parseFloat(currentFormData.opening_stock_value) || 0,
        store_location:
          currentFormData.store_location || formData.store_location || null,
        cabinet: currentFormData.cabinet || formData.cabinet || null,
        row: currentFormData.row || formData.row || null,
      });
    } else {
      console.log("[DEBUG] Excluding variant fields from form data (editing existing product)");
    }

    console.log("Submitting itemData:", itemData);
    console.log("Product name sources:", {
      formDataProductName: formData.product_name,
      productNameState: productName,
      productExists,
      isBatchOnly,
      isNewProduct,
    });
    console.log("Form submission context:", {
      editingVariantIndex,
      initialData: initialData ? 'exists' : 'null',
      existingVariants: existingVariants.length,
      isEditingVariant: editingVariantIndex !== -1
    });
    console.log("Form submission mode:", {
      isNewProduct,
      productExists,
      isBatchOnly,
      hasInitialData: !!initialData,
      willIncludeVariantFields: productExists || isBatchOnly || isNewProduct
    });

    try {
      // Log form submission context
      console.log("[DEBUG][handleSubmit] editingVariantIndex:", editingVariantIndex);
      console.log("[DEBUG][handleSubmit] initialData:", initialData);
      console.log("[DEBUG][handleSubmit] productExists:", productExists);
      console.log("[DEBUG][handleSubmit] isBatchOnly:", isBatchOnly);

      if (productExists || isBatchOnly) {
        // Additional validation before setting pending batch data
        if (!itemData.product_name) {
          toast.error(
            "Product name is missing. Please ensure the product name is properly set."
          );
          console.error("Missing product name in itemData:", itemData);
          return;
        }
        setPendingBatchData(itemData);
        setShowBatchConfirm(true);
      } else {
        // This handles both new product creation and product updates
        console.log("[DEBUG] Before onSubmit - formData.extra_fields:", formData.extra_fields);
        console.log("[DEBUG] Before onSubmit - itemData.extra_fields:", itemData.extra_fields);
        
        await onSubmit(itemData);
        
        // Only reset form if creating new product, not when editing
        if (!initialData) {
          resetToNameInput();
        } else {
          // For editing, refresh the form data from the server to show updated extra fields
          console.log("[DEBUG] Product updated, refreshing form data from server");
          try {
            const response = await axios.get(`http://127.0.0.1:8000/api/products/${initialData.product_id}`);
            const updatedProduct = response.data.data;
            console.log("[DEBUG] Refreshed product data:", updatedProduct);
            console.log("[DEBUG] Refreshed product extra_fields:", updatedProduct.extra_fields);
            
            // Update the form data with the refreshed data
            setFormData(prev => ({
              ...prev,
              extra_fields: updatedProduct.extra_fields || []
            }));
          } catch (error) {
            console.error("[DEBUG] Error refreshing product data:", error);
          }
        }
        
        // Show appropriate success message based on whether we're editing or creating
        if (initialData) {
          toast.success("Product updated successfully");
          // Don't close the form when editing, so user can see the updated extra fields
          console.log("[DEBUG] Product updated successfully, keeping form open");
        } else {
          toast.success("Product created successfully");
          onClose();
        }
      }
    } catch (error) {
      console.error("Error saving product:", error);
      console.log("Full error response:", error.response?.data);
      toast.error(
        error.response?.data?.message ||
          "Failed to save product. Please try again."
      );
      if (error.response?.data?.errors) {
        Object.values(error.response.data.errors).forEach((err) =>
          toast.error(err[0])
        );
      }
    }
  };

  const hasUnsavedChanges = () => {
    return Object.values(formData).some(
      (value) =>
        (typeof value === "string" && value.trim() !== "") ||
        (typeof value === "number" && value !== 0) ||
        (Array.isArray(value) && value.length > 0)
    );
  };

  const handleClose = (e) => {
    e.preventDefault();
    if (isCheckingName && !productName.trim()) {
      resetToNameInput();
      onClose();
      return;
    }
    if (!isCheckingName && hasUnsavedChanges()) {
      setShowCloseConfirm(true);
    } else {
      resetToNameInput();
      onClose();
    }
  };

  const handleConfirmClose = () => {
    resetToNameInput();
    setShowCloseConfirm(false);
    onClose();
  };

  const handleCancelClose = () => {
    setShowCloseConfirm(false);
  };

  // Function to check for duplicate product names
  const checkDuplicateProductName = async (productName) => {
    if (!productName.trim()) {
      setProductNameError("");
      return false;
    }

    try {
      const response = await axios.get(
        "http://127.0.0.1:8000/api/products/check-names",
        {
          params: { names: [productName.trim()] },
        }
      );

      const existingNames = response.data.existing || [];
      const normalizeForComparison = (name) => {
        return name.toLowerCase().replace(/\s+/g, "").trim();
      };

      const normalizedInput = normalizeForComparison(productName.trim());
      const isDuplicate = existingNames.some((existingName) => {
        const normalizedExisting = normalizeForComparison(existingName);
        // If editing, exclude the current product from duplicate check
        if (initialData?.product_name) {
          const normalizedCurrent = normalizeForComparison(
            initialData.product_name
          );
          return (
            normalizedExisting === normalizedInput &&
            normalizedExisting !== normalizedCurrent
          );
        }
        return normalizedExisting === normalizedInput;
      });

      if (isDuplicate) {
        setProductNameError(
          "A product with this name already exists (ignoring case and spaces)"
        );
        return true;
      } else {
        setProductNameError("");
        return false;
      }
    } catch (error) {
      console.error("Error checking duplicate product name:", error);
      setProductNameError("");
      return false;
    }
  };

  // Function to check for duplicate item codes
  const checkDuplicateItemCode = async (itemCode) => {
    if (!itemCode.trim()) {
      setItemCodeError("");
      return false;
    }

    try {
      const response = await axios.get("http://127.0.0.1:8000/api/products", {
        params: { item_code: itemCode.trim() },
      });

      const products = response.data.data || [];
      const isDuplicate = products.some((product) => {
        // If editing, exclude the current product from duplicate check
        if (initialData?.product_id) {
          return (
            product.item_code === itemCode.trim() &&
            product.product_id !== initialData.product_id
          );
        }
        return product.item_code === itemCode.trim();
      });

      if (isDuplicate) {
        setItemCodeError("This item code is already in use");
        return true;
      } else {
        setItemCodeError("");
        return false;
      }
    } catch (error) {
      console.error("Error checking duplicate item code:", error);
      setItemCodeError("");
      return false;
    }
  };

  // Function to check for duplicate batch numbers
  const checkDuplicateBatch = async (batchNumber, productName) => {
    if (!batchNumber.trim() || !productName.trim()) {
      setBatchValidationError("");
      return false;
    }

    try {
      // Get the product to check its existing batches
      const productResponse = await axios.get(
        "http://127.0.0.1:8000/api/products",
        {
          params: { product_name: productName },
        }
      );

      if (productResponse.data.data.length > 0) {
        const product = productResponse.data.data.find(
          (p) =>
            p.product_name.toLowerCase().trim() ===
            productName.toLowerCase().trim()
        );

        if (product && product.variants) {
          const duplicateBatch = product.variants.find(
            (variant) =>
              variant.batch_number &&
              variant.batch_number.toLowerCase().trim() ===
                batchNumber.toLowerCase().trim()
          );

          if (duplicateBatch) {
            setBatchValidationError(
              `Batch number "${batchNumber}" already exists for this product`
            );
            return true;
          }
        }
      }

      setBatchValidationError("");
      return false;
    } catch (error) {
      console.error("Error checking duplicate batch:", error);
      setBatchValidationError("");
      return false;
    }
  };

  // Separate handler for batch form data
  const handleBatchChange = async (e) => {
    let { name, value } = e.target;

    const parseOrZero = (val) => {
      const parsed = parseFloat(val);
      return isNaN(parsed) ? 0 : parsed;
    };

    // Update the form data with the raw input value (no formatting)
    setBatchFormData((prevData) => ({ ...prevData, [name]: value }));

    // Check for duplicate batch numbers when batch_number changes
    if (name === "batch_number" && (productExists || isBatchOnly)) {
      const currentProductName = formData.product_name || productName;
      if (currentProductName) {
        await checkDuplicateBatch(value, currentProductName);
      }
    }

    // Handle percentage calculations and auto-calculations
    if (name === "buying_cost") {
      const buyingCost = parseOrZero(value);
      const updatedData = {};

      // Only calculate if percentage is set (not zero)
      if (salesPricePercentage && parseFloat(salesPricePercentage) > 0) {
        updatedData.sales_price = (
          buyingCost +
          buyingCost * (parseFloat(salesPricePercentage) / 100)
        ).toFixed(2);
      }

      if (
        wholesalePricePercentage &&
        parseFloat(wholesalePricePercentage) > 0
      ) {
        updatedData.wholesale_price = (
          buyingCost +
          buyingCost * (parseFloat(wholesalePricePercentage) / 100)
        ).toFixed(2);
      }

      if (minimumPricePercentage && parseFloat(minimumPricePercentage) > 0) {
        updatedData.minimum_price = (
          buyingCost +
          buyingCost * (parseFloat(minimumPricePercentage) / 100)
        ).toFixed(2);
      }

      if (Object.keys(updatedData).length > 0) {
        setBatchFormData((prevData) => ({ ...prevData, ...updatedData }));
      }
    } else if (name === "sales_price") {
      // Only update percentage, don't modify the input value
      const buyingCost = parseOrZero(batchFormData.buying_cost);
      const salesPrice = parseOrZero(value);
      const percentage =
        buyingCost === 0 ? 0 : ((salesPrice - buyingCost) / buyingCost) * 100;
      setSalesPricePercentage(percentage.toFixed(2));
    } else if (name === "wholesale_price") {
      // Only update percentage, don't modify the input value
      const buyingCost = parseOrZero(batchFormData.buying_cost);
      const wholesalePrice = parseOrZero(value);
      const percentage =
        buyingCost === 0
          ? 0
          : ((wholesalePrice - buyingCost) / buyingCost) * 100;
      setWholesalePricePercentage(percentage.toFixed(2));
    } else if (name === "minimum_price") {
      // Only update percentage, don't modify the input value
      const buyingCost = parseOrZero(batchFormData.buying_cost);
      const minimumPrice = parseOrZero(value);
      const percentage =
        buyingCost === 0 ? 0 : ((minimumPrice - buyingCost) / buyingCost) * 100;
      setMinimumPricePercentage(percentage.toFixed(2));
    }

    // Calculate opening stock value for batch form (only when not directly editing opening_stock_value)
    if (
      (name === "opening_stock_quantity" || name === "buying_cost") &&
      name !== "opening_stock_value"
    ) {
      const quantity =
        name === "opening_stock_quantity"
          ? parseOrZero(value)
          : parseOrZero(batchFormData.opening_stock_quantity);
      const cost =
        name === "buying_cost"
          ? parseOrZero(value)
          : parseOrZero(batchFormData.buying_cost);
      const opening_stock_value = quantity * cost;

      // Use setTimeout to avoid conflicting with the main state update
      setTimeout(() => {
        setBatchFormData((prevData) => ({
          ...prevData,
          opening_stock_value: opening_stock_value.toFixed(2),
        }));
      }, 0);
    }
  };

  const handleBlur = async (fieldName) => {
    if (productExists || isBatchOnly) {
      // Handle batch form data
      setBatchFormData((prevData) => {
        const updatedData = { ...prevData };

        if (fieldName === "barcode" && !updatedData.barcode) {
          updatedData.barcode = `BAR${Math.floor(1000 + Math.random() * 9000)}`;
          axios
            .get("http://127.0.0.1:8000/api/products/check-barcode", {
              params: { barcode: updatedData.barcode },
            })
            .then((response) => {
              if (response.data.exists) {
                toast.error(
                  "Generated barcode already exists. Please try again."
                );
                setBatchFormData((prev) => ({ ...prev, barcode: "" }));
              }
            })
            .catch((error) => {
              console.error("Error checking barcode:", error);
            });
        }

        if (
          fieldName === "opening_stock_quantity" ||
          fieldName === "buying_cost"
        ) {
          const openingStockValue =
            (parseFloat(updatedData.opening_stock_quantity) || 0) *
            (parseFloat(updatedData.buying_cost) || 0);
          updatedData.opening_stock_value = openingStockValue.toFixed(2);
        }

        return updatedData;
      });
    } else {
      // Handle main form data
      setFormData((prevData) => {
        const updatedData = { ...prevData };

        if (fieldName === "item_code" && !updatedData.item_code) {
          updatedData.item_code = `ITM${Math.floor(1000 + Math.random() * 9000)}`;
          axios
            .get("http://127.0.0.1:8000/api/products/check-item-code", {
              params: { item_code: updatedData.item_code },
            })
            .then((response) => {
              if (response.data.exists) {
                toast.error(
                  "Generated item code already exists. Please try again."
                );
                setFormData((prev) => ({ ...prev, item_code: "" }));
              }
            })
            .catch((error) => {
              console.error("Error checking item code:", error);
            });
        }

        if (fieldName === "barcode" && !updatedData.barcode) {
          updatedData.barcode = `BAR${Math.floor(1000 + Math.random() * 9000)}`;
          axios
            .get("http://127.0.0.1:8000/api/products/check-barcode", {
              params: { barcode: updatedData.barcode },
            })
            .then((response) => {
              if (response.data.exists) {
                toast.error(
                  "Generated barcode already exists. Please try again."
                );
                setFormData((prev) => ({ ...prev, barcode: "" }));
              }
            })
            .catch((error) => {
              console.error("Error checking barcode:", error);
            });
        }

        if (
          fieldName === "opening_stock_quantity" ||
          fieldName === "buying_cost"
        ) {
          const openingStockValue =
            (parseFloat(updatedData.opening_stock_quantity) || 0) *
            (parseFloat(updatedData.buying_cost) || 0);
          updatedData.opening_stock_value = openingStockValue.toFixed(2);
        }

        return updatedData;
      });
    }
  };

  const handleKeyDown = (e, nextField) => {
    if (e.key === "Enter") {
      e.preventDefault();

      // Map field names to refs for better navigation
      const fieldRefMap = {
        product_name: productNameRef,
        item_code: itemCodeRef,
        category: categoryRef,
        supplier: supplierRef,
        unit_type: unitTypeRef,
        company: companyRef,
        batch_number: batchNumberRef,
        barcode: barcodeRef,
        expiry_date: expiryDateRef,
        buying_cost: buyingCostRef,
        sales_price: salesPriceRef,
        sales_price_percentage: salesPricePercentageRef,
        wholesale_price: wholesalePriceRef,
        wholesale_price_percentage: wholesalePricePercentageRef,
        minimum_price: minimumPriceRef,
        minimum_price_percentage: minimumPricePercentageRef,
        mrp: mrpRef,
        minimum_stock_quantity: minimumStockRef,
        opening_stock_quantity: openingStockRef,
        opening_stock_value: openingStockValueRef,
        store_location: storeLocationRef,
        cabinet: cabinetRef,
        row: rowRef,
        submit_button: submitButtonRef,
      };

      // Special handling for submit button
      if (nextField === "submit_button") {
        const submitRef = fieldRefMap[nextField];
        if (submitRef && submitRef.current) {
          // Trigger form submission instead of just focusing
          submitRef.current.click();
        }
        return;
      }

      // Try to use ref first, then fallback to querySelector
      const nextRef = fieldRefMap[nextField];
      if (nextRef && nextRef.current) {
        nextRef.current.focus();
      } else {
        const nextInput = document.querySelector(`[name="${nextField}"]`);
        if (nextInput) {
          nextInput.focus();
        }
      }
    }
  };

  // Add state for product suggestions
  const [productSuggestions, setProductSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const suggestionsRef = useRef(null);

  // Fetch product suggestions as user types
  useEffect(() => {
    if (isCheckingName && !initialData && productName.trim()) {
      const fetchSuggestions = async () => {
        try {
          const response = await axios.get("http://127.0.0.1:8000/api/products", {
            params: { search: productName.trim() },
          });
          if (response.data && Array.isArray(response.data.data)) {
            setProductSuggestions(
              response.data.data.map((p) => p.product_name).filter(Boolean)
            );
          } else {
            setProductSuggestions([]);
          }
        } catch (error) {
          setProductSuggestions([]);
        }
      };
      fetchSuggestions();
      setShowSuggestions(true);
    } else {
      setProductSuggestions([]);
      setShowSuggestions(false);
    }
  }, [productName, isCheckingName, initialData]);

  // Handle keyboard navigation for suggestions
  const handleProductNameKeyDown = (e) => {
    if (!showSuggestions || productSuggestions.length === 0) return;
    if (e.key === "ArrowDown") {
      e.preventDefault();
      setHighlightedIndex((prev) =>
        prev < productSuggestions.length - 1 ? prev + 1 : 0
      );
    } else if (e.key === "ArrowUp") {
      e.preventDefault();
      setHighlightedIndex((prev) =>
        prev > 0 ? prev - 1 : productSuggestions.length - 1
      );
    } else if (e.key === "Enter" && highlightedIndex >= 0) {
      e.preventDefault();
      setProductName(productSuggestions[highlightedIndex]);
      setShowSuggestions(false);
      setHighlightedIndex(-1);
      if (checkButtonRef.current) {
        checkButtonRef.current.focus();
      }
    } else if (e.key === "Escape") {
      setShowSuggestions(false);
      setHighlightedIndex(-1);
    }
  };

  if (showNameCheckResult && !initialData) {
    return (
      <div className="fixed inset-0 z-50 backdrop-blur-sm bg-black/30 flex items-center justify-center">
        <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-lg max-w-md w-full">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">
            Product Name Check Result
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            {productExists
              ? `Product '${productName}' already exists. Proceed to add a new batch?`
              : `Product '${productName}' does not exist. Proceed to create a new product?`}
          </p>
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={handleNameCheckCancel}
              className="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500"
            >
              Cancel
            </button>
            <button
              ref={proceedButtonRef}
              type="button"
              onClick={handleNameCheckConfirm}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  e.preventDefault();
                  handleNameCheckConfirm();
                }
              }}
              className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
              autoFocus
            >
              Proceed
            </button>
          </div>
        </div>
        <ToastContainer />
      </div>
    );
  }

  if (isCheckingName && !initialData) {
    return (
      <div className="fixed inset-0 z-50 backdrop-blur-sm bg-black/30 flex items-center justify-center">
        <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-lg max-w-md w-full">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">
            Enter Product Name
          </h2>
          <form onSubmit={handleNameSubmit} autoComplete="off">
            <div className="space-y-4 relative">
              <input
                ref={productNameInputRef}
                type="text"
                value={productName}
                onChange={(e) => {
                  setProductName(e.target.value);
                  setHighlightedIndex(-1);
                }}
                onKeyDown={handleProductNameKeyDown}
                placeholder="Enter product name"
                className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                autoFocus
                autoComplete="off"
                onBlur={() => setTimeout(() => setShowSuggestions(false), 150)}
                onFocus={() => productSuggestions.length > 0 && setShowSuggestions(true)}
              />
              {/* Suggestions Dropdown */}
              {showSuggestions && productSuggestions.length > 0 && (
                <ul
                  ref={suggestionsRef}
                  className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-y-auto"
                  role="listbox"
                >
                  {productSuggestions.map((name, idx) => (
                    <li
                      key={name + idx}
                      onClick={() => {
                        setProductName(name);
                        setShowSuggestions(false);
                        setHighlightedIndex(-1);
                        if (checkButtonRef.current) checkButtonRef.current.focus();
                      }}
                      onMouseEnter={() => setHighlightedIndex(idx)}
                      className={`p-2 cursor-pointer text-gray-900 dark:text-white hover:bg-blue-100 dark:hover:bg-blue-600 ${
                        idx === highlightedIndex ? "bg-blue-100 dark:bg-blue-600" : ""
                      }`}
                      role="option"
                      aria-selected={idx === highlightedIndex}
                    >
                      {name}
                    </li>
                  ))}
                </ul>
              )}
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={handleClose}
                  className="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500"
                >
                  Cancel
                </button>
                <button
                  ref={checkButtonRef}
                  type="submit"
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                      handleNameSubmit(e);
                    }
                  }}
                  className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                >
                  Check
                </button>
              </div>
            </div>
          </form>
          <ToastContainer />
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="fixed inset-0 z-50 backdrop-blur-sm bg-black/30">
        <form
          onSubmit={(e) => {
            // Prevent form submission if we're editing a variant
            if (editingVariantIndex !== -1) {
              e.preventDefault();
              console.log("[DEBUG] Form onSubmit blocked - currently editing variant");
              return false;
            }
            handleSubmit(e);
          }}
          className="bg-slate-100 dark:bg-gray-800 w-full min-w-max h-screen overflow-y-auto"
        >
          <div className="max-w-7xl mx-auto px-6 py-8">
            <div className="flex items-center justify-between pb-6 border-b border-gray-200 dark:border-gray-700">
              <div>
                <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-100">
                  {productExists || isBatchOnly
                    ? "Add Batch"
                    : initialData
                      ? "Edit Product"
                      : "Create New Product"}
                </h2>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  {productExists || isBatchOnly
                    ? "Add a new batch for existing product"
                    : initialData
                      ? "Update product details"
                      : "Add a new product to inventory"}
                </p>
              </div>
              <button
                type="button"
                onClick={handleClose}
                className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                aria-label="Close"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            <div className="mt-8 space-y-8">
              <div className="bg-white dark:bg-gray-900 p-8 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 max-w-full mx-auto">
                <h3 className="text-2xl font-semibold text-center text-gray-800 dark:text-white mb-8">
                  Basic Information
                </h3>
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                  {/* Product Name - Show for new products and edit mode, hide only for batch-only mode */}
                  {!isBatchOnly && (
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Product Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        ref={productNameRef}
                        type="text"
                        name="product_name"
                        value={formData.product_name}
                        onChange={(e) => {
                          handleChange(e);
                          // Check for duplicate product name on change
                          if (e.target.value.trim()) {
                            checkDuplicateProductName(e.target.value);
                          } else {
                            setProductNameError("");
                          }
                        }}
                        onKeyDown={(e) => handleKeyDown(e, !isBatchOnly && !productExists ? "item_code" : "batch_number")}
                        className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white ${
                          productNameError
                            ? "border-red-500 dark:border-red-400"
                            : "border-gray-300 dark:border-gray-600"
                        }`}
                        placeholder="Enter the Item Name"
                        required
                        disabled={initialData && productExists} // Only disable if editing existing product with confirmed existence
                      />
                      {productNameError && (
                        <p className="text-red-500 text-sm mt-1">
                          {productNameError}
                        </p>
                      )}
                    </div>
                  )}

                  {/* Product Name for Batch Mode - Show product name as disabled field */}
                  {isBatchOnly && (
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Product Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        ref={productNameRef}
                        type="text"
                        name="product_name"
                        value={formData.product_name || initialData?.product_name || ""}
                        onKeyDown={(e) => handleKeyDown(e, "batch_number")}
                        className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-gray-100 dark:bg-gray-600 dark:text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="Product Name"
                        disabled
                        tabIndex={0}
                      />
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Product name is pre-filled for batch addition
                      </p>
                    </div>
                  )}

                  {/* Item Code - Show for new products and edit mode, hide in batch mode */}
                  {!isBatchOnly && !productExists && (
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Item Code
                      </label>
                      <input
                        ref={itemCodeRef}
                        type="text"
                        name="item_code"
                        value={formData.item_code}
                        onChange={(e) => {
                          handleChange(e);
                          // Check for duplicate item code on change
                          if (e.target.value.trim()) {
                            checkDuplicateItemCode(e.target.value);
                          } else {
                            setItemCodeError("");
                          }
                        }}
                        onBlur={() => handleBlur("item_code")}
                        onKeyDown={(e) => handleKeyDown(e, "batch_number")}
                        className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white ${
                          itemCodeError
                            ? "border-red-500 dark:border-red-400"
                            : "border-gray-300 dark:border-gray-600"
                        }`}
                        placeholder="e.g. ITM-0001"
                      />
                      {itemCodeError && (
                        <p className="text-red-500 text-sm mt-1">
                          {itemCodeError}
                        </p>
                      )}
                    </div>
                  )}



                  {/* Batch Number - Only for add forms, not edit product */}
                  {!initialData && (
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Batch Number
                      </label>
                      <input
                        ref={batchNumberRef}
                        type="text"
                        name="batch_number"
                        value={
                          productExists || isBatchOnly
                            ? batchFormData.batch_number
                            : formData.batch_number || ""
                        }
                        onChange={
                          productExists || isBatchOnly
                            ? handleBatchChange
                            : handleChange
                        }
                        onKeyDown={(e) => handleKeyDown(e, "barcode")}
                        className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white ${
                          batchValidationError
                            ? "border-red-500 dark:border-red-400"
                            : "border-gray-300 dark:border-gray-600"
                        }`}
                        placeholder="Enter batch number"
                      />
                      {batchValidationError && (
                        <p className="text-red-500 text-sm mt-1">
                          {batchValidationError}
                        </p>
                      )}
                    </div>
                  )}

                  {/* Barcode - Only for add forms, not edit product */}
                  {!initialData && (
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Barcode
                      </label>
                      <input
                        ref={barcodeRef}
                        type="text"
                        name="barcode"
                        value={
                          productExists || isBatchOnly
                            ? batchFormData.barcode || ""
                            : formData.barcode || ""
                        }
                        onChange={
                          productExists || isBatchOnly
                            ? handleBatchChange
                            : handleChange
                        }
                        onBlur={async (e) => {
                          handleBlur("barcode");
                          if (e.target.value.trim()) {
                            await fetchVariantsByBarcode(e.target.value.trim());
                          }
                        }}
                        onKeyDown={(e) => handleKeyDown(e, "expiry_date")}
                        className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                        placeholder="Auto-generated if empty"
                      />
                    </div>
                  )}

                  {/* Expiry Date - Only for add forms, not edit product */}
                  {!initialData && (
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Expiry Date
                      </label>
                      <input
                        ref={expiryDateRef}
                        type="date"
                        name="expiry_date"
                        value={
                          productExists || isBatchOnly
                            ? batchFormData.expiry_date &&
                              !isNaN(Date.parse(batchFormData.expiry_date))
                              ? batchFormData.expiry_date.split("T")[0]
                              : ""
                            : formData.expiry_date &&
                                !isNaN(Date.parse(formData.expiry_date))
                              ? formData.expiry_date.split("T")[0]
                              : ""
                        }
                        onChange={
                          productExists || isBatchOnly
                            ? handleBatchChange
                            : handleChange
                        }
                        onKeyDown={(e) => handleKeyDown(e, "mrp")}
                        className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                      />
                    </div>
                  )}

                  {/* MRP - Only for add forms, not edit product */}
                  {!initialData && (
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        MRP <span className="text-red-500">*</span>
                      </label>
                      <input
                        ref={mrpRef}
                        type="number"
                        step="0.01"
                        name="mrp"
                        value={
                          productExists || isBatchOnly
                            ? batchFormData.mrp
                            : formData.mrp || ""
                        }
                        onChange={
                          productExists || isBatchOnly
                            ? handleBatchChange
                            : handleChange
                        }
                        onKeyDown={(e) => handleKeyDown(e, "buying_cost")}
                        className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                        placeholder="0.00"
                        required
                      />
                    </div>
                  )}
                </div>
              </div>
              {/* Pricing Details Section
              <div className="bg-white dark:bg-gray-900 p-8 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 max-w-full mx-auto">
                <h3 className="text-2xl font-semibold text-center text-gray-800 dark:text-white mb-8">
                  Pricing Details
                </h3>
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                  <div className="space-y-1">
                    <label className="flex items-center justify-between text-sm font-medium text-gray-700 dark:text-gray-300">
                      Category
                      <button
                        type="button"
                        onClick={() => setShowAddCategory(true)}
                        className="text-indigo-600 hover:text-indigo-900 text-xs font-normal"
                      >
                        + Add New
                      </button>
                    </label>
                    <select
                      name="category"
                      value={formData.category}
                      onChange={handleChange}
                      onKeyDown={(e) => handleKeyDown(e, "supplier")}
                      className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                    >
                      <option key="select-category" value="">Select Category</option>
                      {categories.map((cat) => (
                        <option key={cat.id} value={cat.name}>
                          {cat.name}
                        </option>
                      ))}
                    </select>
                    {showAddCategory && (
                      <div className="mt-2 flex space-x-2">
                        <input
                          type="text"
                          value={newCategory}
                          onChange={(e) => setNewCategory(e.target.value)}
                          onKeyDown={(e) => handleKeyDown(e, "category_button")}
                          placeholder="New category name"
                          className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                        />
                        <button
                          name="category_button"
                          type="button"
                          onClick={handleAddCategory}
                          className="px-3 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                        >
                          Add
                        </button>
                        <button
                          type="button"
                          onClick={() => {
                            setShowAddCategory(false);
                            setNewCategory("");
                          }}
                          className="px-3 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500"
                        >
                          Cancel
                        </button>
                      </div>
                    )}
                  </div>

                  <div className="space-y-1">
                    <label className="flex items-center justify-between text-sm font-medium text-gray-700 dark:text-gray-300">
                      Supplier
                    </label>
                    <select
                      name="supplier"
                      value={formData.supplier}
                      onChange={handleChange}
                      onKeyDown={(e) => handleKeyDown(e, "unit_type")}
                      className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                    >
                      <option key="select-supplier-1" value="">Select Supplier</option>
                      {suppliers.map((supplier, index) => (
                        <option
                          key={supplier.supplier_id ?? supplier.supplier_name ?? index}
                          value={supplier.supplier_name}
                        >
                          {supplier.supplier_name}
                        </option>
                      ))}
                    </select>
                    {showAddSupplier && (
                      <div className="mt-2 space-y-2">
                        <input
                          type="text"
                          value={newSupplier}
                          onChange={(e) => setNewSupplier(e.target.value)}
                          placeholder="New supplier name"
                          onKeyDown={(e) => handleKeyDown(e, "contact")}
                          className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                        />
                        <input
                          type="text"
                          name="contact"
                          value={formData.contact}
                          onChange={(e) =>
                            setFormData((prev) => ({
                              ...prev,
                              contact: e.target.value,
                            }))
                          }
                          onKeyDown={(e) => handleKeyDown(e, "address")}
                          placeholder="Contact"
                          className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                        />
                        <input
                          type="text"
                          name="address"
                          value={formData.address}
                          onChange={(e) =>
                            setFormData((prev) => ({
                              ...prev,
                              address: e.target.value,
                            }))
                          }
                          onKeyDown={(e) => handleKeyDown(e, "supplier_button")}
                          placeholder="Address"
                          className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                        />
                        <div className="flex space-x-2">
                          <button
                            name="supplier_button"
                            type="button"
                            onClick={handleAddSupplier}
                            className="px-3 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                          >
                            Add
                          </button>
                          <button
                            type="button"
                            onClick={() => {
                              setShowAddSupplier(false);
                              setNewSupplier("");
                              setFormData((prev) => ({
                                ...prev,
                                contact: "",
                                address: "",
                              }));
                            }}
                            className="px-3 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500"
                          >
                            Cancel
                          </button>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="space-y-1">
                    <label className="flex items-center justify-between text-sm font-medium text-gray-700 dark:text-gray-300">
                      Unit Type
                    </label>
                    <select
                      name="unit_type"
                      value={formData.unit_type}
                      onChange={handleChange}
                      onKeyDown={(e) => handleKeyDown(e, "store_location")}
                      className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                    >
                      <option key="select-unit-type" value="">Select Unit Type</option>
                      {unitTypes.map((unit) => (
                        <option key={unit.id} value={unit.unit_name}>
                          {unit.unit_name}
                        </option>
                      ))}
                    </select>
                    {showAddUnitType && (
                      <div className="mt-2 flex space-x-2">
                        <input
                          type="text"
                          value={newUnitType}
                          onChange={(e) => setNewUnitType(e.target.value)}
                          onKeyDown={(e) => handleKeyDown(e, "unit_button")}
                          placeholder="New unit type name"
                          className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600"
                        />
                        <button
                          name="unit_button"
                          type="button"
                          onClick={handleAddUnitType}
                          className="px-3 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                        >
                          Add
                        </button>
                        <button
                          type="button"
                          onClick={() => {
                            setShowAddUnitType(false);
                            setNewUnitType("");
                          }}
                          className="px-3 py-2 bg-gray-300 dark:bg-gray-600 text-gray-900 dark:text-gray-300 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500"
                        >
                          Cancel
                        </button>
                      </div>
                    )}
                  </div>

                  <div className="space-y-1">
                    <label className="flex items-center justify-between text-sm font-medium text-gray-700 dark:text-gray-300">
                      Company
                    </label>
                    <select
                      name="company"
                      value={formData.company}
                      onChange={handleChange}
                      className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                    >
                      <option key="select-company" value="">Select Company</option>
                      {companies.map((comp) => (
                        <option key={comp.id} value={comp.company_name}>
                          {comp.company_name}
                        </option>
                      ))}
                    </select>
                    {showAddCompany && (
                      <div className="mt-2 space-y-2 p-4 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700">
                        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Add New Company</h4>
                        <input
                          type="text"
                          value={newCompanyData.company_name}
                          onChange={(e) => setNewCompanyData(prev => ({...prev, company_name: e.target.value}))}
                          placeholder="Company Name *"
                          className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                          required
                        />
                        <input
                          type="text"
                          value={newCompanyData.contact}
                          onChange={(e) => setNewCompanyData(prev => ({...prev, contact: e.target.value}))}
                          placeholder="Contact"
                          className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                        />
                        <input
                          type="text"
                          value={newCompanyData.address}
                          onChange={(e) => setNewCompanyData(prev => ({...prev, address: e.target.value}))}
                          placeholder="Address"
                          className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                        />
                        <input
                          type="number"
                          value={newCompanyData.opening_balance}
                          onChange={(e) => setNewCompanyData(prev => ({...prev, opening_balance: e.target.value}))}
                          placeholder="Opening Balance"
                          step="0.01"
                          className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                        />
                        <div className="flex space-x-2">
                          <button
                            name="company_button"
                            type="button"
                            onClick={handleAddCompany}
                            className="px-3 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                          >
                            Add Company
                          </button>
                          <button
                            type="button"
                            onClick={() => {
                              setShowAddCompany(false);
                              setNewCompanyData({
                                company_name: "",
                                contact: "",
                                address: "",
                                opening_balance: "",
                              });
                              setNewCompany("");
                            }}
                            className="px-3 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500"
                          >
                            Cancel
                          </button>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="space-y-1">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Branch Name
                    </label>
                    <input
                      type="text"
                      name="branch_name"
                      value={formData.branch_name}
                      onChange={handleChange}
                      className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                      placeholder="Enter Branch Name"
                    />
                  </div>

                  <div className="space-y-1">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Branch Quantity
                    </label>
                    <input
                      type="number"
                      name="branch_qty"
                      value={formData.branch_qty}
                      onChange={handleChange}
                      className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                      placeholder="Enter Branch Quantity"
                    />
                  </div>
                </div>
              </div> */}
              {/* Pricing Details Section - Show for ADD forms (new products and batch creation) */}
              {editingVariantIndex === -1 && !initialData && (
                <div className="bg-white dark:bg-gray-900 p-8 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 max-w-full mx-auto">
                  <h3 className="text-2xl font-semibold text-center text-gray-800 dark:text-white mb-8">
                    Pricing Details
                  </h3>
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                    {/* Buying Cost */}
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Buying Cost
                      </label>
                      <input
                        ref={buyingCostRef}
                        type="number"
                        step="0.01"
                        name="buying_cost"
                        value={
                          productExists || isBatchOnly
                            ? batchFormData.buying_cost
                            : formData.buying_cost || ""
                        }
                        onChange={
                          productExists || isBatchOnly
                            ? handleBatchChange
                            : handleChange
                        }
                        onKeyDown={(e) => handleKeyDown(e, "sales_price")}
                        className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                        placeholder="0.00"
                      />
                    </div>

                    {/* Sales Price */}
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Sales Price <span className="text-red-500">*</span>
                      </label>
                      <div className="flex space-x-2">
                        <input
                          ref={salesPriceRef}
                          type="number"
                          step="0.01"
                          name="sales_price"
                          value={
                            productExists || isBatchOnly
                              ? batchFormData.sales_price
                              : formData.sales_price || ""
                          }
                          onChange={
                            productExists || isBatchOnly
                              ? handleBatchChange
                              : handleChange
                          }
                          onKeyDown={(e) =>
                            handleKeyDown(e, "sales_price_percentage")
                          }
                          className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                          placeholder="0.00"
                          required
                        />
                        <div className="relative">
                          <input
                            ref={salesPricePercentageRef}
                            type="number"
                            step="0.01"
                            name="sales_price_percentage"
                            value={salesPricePercentage}
                            onChange={(e) => {
                              const percentage = e.target.value;
                              setSalesPricePercentage(percentage);

                              // Auto-calculate sales price when percentage is entered
                              if (percentage && parseFloat(percentage) > 0) {
                                const buyingCostValue =
                                  productExists || isBatchOnly
                                    ? batchFormData.buying_cost
                                    : formData.buying_cost;
                                const buyingCost =
                                  parseFloat(buyingCostValue) || 0;

                                if (buyingCost > 0) {
                                  const calculatedPrice = (
                                    buyingCost +
                                    buyingCost * (parseFloat(percentage) / 100)
                                  ).toFixed(2);

                                  if (productExists || isBatchOnly) {
                                    setBatchFormData((prev) => ({
                                      ...prev,
                                      sales_price: calculatedPrice,
                                    }));
                                  } else {
                                    setFormData((prev) => ({
                                      ...prev,
                                      sales_price: calculatedPrice,
                                    }));
                                  }
                                }
                              }
                            }}
                            onKeyDown={(e) =>
                              handleKeyDown(e, "wholesale_price_percentage")
                            }
                            className="w-20 px-2 py-2 pr-6 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                            placeholder="0.00"
                          />
                          <span className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400 text-sm pointer-events-none">
                            %
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Wholesale Price */}
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Wholesale Price
                      </label>
                      <div className="flex space-x-2">
                        <input
                          ref={wholesalePriceRef}
                          type="number"
                          step="0.01"
                          name="wholesale_price"
                          value={
                            productExists || isBatchOnly
                              ? batchFormData.wholesale_price
                              : formData.wholesale_price || ""
                          }
                          onChange={
                            productExists || isBatchOnly
                              ? handleBatchChange
                              : handleChange
                          }
                          onKeyDown={(e) =>
                            handleKeyDown(e, "wholesale_price_percentage")
                          }
                          className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                          placeholder="0.00"
                        />
                        <div className="relative">
                          <input
                            ref={wholesalePricePercentageRef}
                            type="number"
                            step="0.01"
                            name="wholesale_price_percentage"
                            value={wholesalePricePercentage}
                            onChange={(e) => {
                              const percentage = e.target.value;
                              setWholesalePricePercentage(percentage);

                              // Auto-calculate wholesale price when percentage is entered
                              if (percentage && parseFloat(percentage) > 0) {
                                const buyingCostValue =
                                  productExists || isBatchOnly
                                    ? batchFormData.buying_cost
                                    : formData.buying_cost;
                                const buyingCost =
                                  parseFloat(buyingCostValue) || 0;

                                if (buyingCost > 0) {
                                  const calculatedPrice = (
                                    buyingCost +
                                    buyingCost * (parseFloat(percentage) / 100)
                                  ).toFixed(2);

                                  if (productExists || isBatchOnly) {
                                    setBatchFormData((prev) => ({
                                      ...prev,
                                      wholesale_price: calculatedPrice,
                                    }));
                                  } else {
                                    setFormData((prev) => ({
                                      ...prev,
                                      wholesale_price: calculatedPrice,
                                    }));
                                  }
                                }
                              }
                            }}
                            onKeyDown={(e) => handleKeyDown(e, "minimum_price")}
                            className="w-20 px-2 py-2 pr-6 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                            placeholder="0.00"
                          />
                          <span className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400 text-sm pointer-events-none">
                            %
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Minimum Price */}
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Minimum Price
                      </label>
                      <div className="flex space-x-2">
                        <input
                          ref={minimumPriceRef}
                          type="number"
                          step="0.01"
                          name="minimum_price"
                          value={
                            productExists || isBatchOnly
                              ? batchFormData.minimum_price
                              : formData.minimum_price || ""
                          }
                          onChange={
                            productExists || isBatchOnly
                              ? handleBatchChange
                              : handleChange
                          }
                          onKeyDown={(e) =>
                            handleKeyDown(e, "minimum_price_percentage")
                          }
                          className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                          placeholder="0.00"
                        />
                        <div className="relative">
                          <input
                            ref={minimumPricePercentageRef}
                            type="number"
                            step="0.01"
                            name="minimum_price_percentage"
                            value={minimumPricePercentage}
                            onChange={(e) => {
                              const percentage = e.target.value;
                              setMinimumPricePercentage(percentage);

                              // Auto-calculate minimum price when percentage is entered
                              if (percentage && parseFloat(percentage) > 0) {
                                const buyingCostValue =
                                  productExists || isBatchOnly
                                    ? batchFormData.buying_cost
                                    : formData.buying_cost;
                                const buyingCost =
                                  parseFloat(buyingCostValue) || 0;

                                if (buyingCost > 0) {
                                  const calculatedPrice = (
                                    buyingCost +
                                    buyingCost * (parseFloat(percentage) / 100)
                                  ).toFixed(2);

                                  if (productExists || isBatchOnly) {
                                    setBatchFormData((prev) => ({
                                      ...prev,
                                      minimum_price: calculatedPrice,
                                    }));
                                  } else {
                                    setFormData((prev) => ({
                                      ...prev,
                                      minimum_price: calculatedPrice,
                                    }));
                                  }
                                }
                              }
                            }}
                            onKeyDown={(e) =>
                              handleKeyDown(e, "minimum_stock_quantity")
                            }
                            className="w-20 px-2 py-2 pr-6 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                            placeholder="0.00"
                          />
                          <span className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400 text-sm pointer-events-none">
                            %
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              {/* Inventory Information Section - Only show for ADD forms (new products and batch creation), not for any EDIT forms */}
              {editingVariantIndex === -1 && !initialData && (
                <div className="bg-white dark:bg-gray-900 p-8 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 max-w-full mx-auto">
                  <h3 className="text-2xl font-semibold text-center text-gray-800 dark:text-white mb-8">
                    Inventory Information
                  </h3>
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                    {/* Minimum Stock Quantity */}
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Minimum Stock Quantity
                      </label>
                      <input
                        ref={minimumStockRef}
                        type="number"
                        step="0.01"
                        name="minimum_stock_quantity"
                        value={
                          productExists || isBatchOnly
                            ? batchFormData.minimum_stock_quantity
                            : formData.minimum_stock_quantity || ""
                        }
                        onChange={
                          productExists || isBatchOnly
                            ? handleBatchChange
                            : handleChange
                        }
                        onKeyDown={(e) =>
                          handleKeyDown(e, "opening_stock_quantity")
                        }
                        className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                        placeholder="0"
                      />
                    </div>

                    {/* Opening Stock Quantity */}
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Opening Stock Quantity
                      </label>
                      <input
                        ref={openingStockRef}
                        type="number"
                        step="0.01"
                        name="opening_stock_quantity"
                        value={
                          productExists || isBatchOnly
                            ? batchFormData.opening_stock_quantity
                            : formData.opening_stock_quantity || ""
                        }
                        onChange={
                          productExists || isBatchOnly
                            ? handleBatchChange
                            : handleChange
                        }
                        onBlur={() => handleBlur("opening_stock_quantity")}
                        onKeyDown={(e) => handleKeyDown(e, "opening_stock_value")}
                        className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                        placeholder="0"
                      />
                    </div>

                    {/* Opening Stock Value */}
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Opening Stock Value (Auto-calculated)
                      </label>
                      <input
                        ref={openingStockValueRef}
                        type="number"
                        step="0.01"
                        name="opening_stock_value"
                        value={
                          productExists || isBatchOnly
                            ? batchFormData.opening_stock_value
                            : formData.opening_stock_value || ""
                        }
                        onChange={
                          productExists || isBatchOnly
                            ? handleBatchChange
                            : handleChange
                        }
                        onKeyDown={(e) => {
                          if (productExists || isBatchOnly) {
                            // In batch mode, navigate to storage section
                            handleKeyDown(e, "store_location");
                          } else {
                            // In create mode, navigate to classification section
                            handleKeyDown(e, "category");
                          }
                        }}
                        className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white bg-gray-100 dark:bg-gray-600"
                        placeholder="0.00"
                        readOnly
                      />
                    </div>
                  </div>
                </div>
              )}
              {/* Classification Information Section - Show for new products and edit mode */}
              {(!(productExists || isBatchOnly) || initialData) && (
                <div className="bg-white dark:bg-gray-900 p-8 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 max-w-full mx-auto">
                  <h3 className="text-2xl font-semibold text-center text-gray-800 dark:text-white mb-8">
                    Classification Information
                  </h3>
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                    <div className="space-y-1">
                      <label className="flex items-center justify-between text-sm font-medium text-gray-700 dark:text-gray-300">
                        Category
                        <button
                          type="button"
                          onClick={() => setShowAddCategory(true)}
                          className="text-indigo-600 hover:text-indigo-900 text-xs font-normal"
                        >
                          + Add New
                        </button>
                      </label>
                      <select
                        ref={categoryRef}
                        name="category"
                        value={formData.category}
                        onChange={handleChange}
                        onKeyDown={(e) => handleKeyDown(e, "supplier")}
                        className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                      >
                        <option key="select-category" value="">
                          Select Category
                        </option>
                        {categories.map((cat) => (
                          <option key={cat.id} value={cat.name}>
                            {cat.name}
                          </option>
                        ))}
                      </select>
                      {showAddCategory && (
                        <div className="mt-2 flex space-x-2">
                          <input
                            type="text"
                            value={newCategory}
                            onChange={(e) => setNewCategory(e.target.value)}
                            onKeyDown={(e) => handleKeyDown(e, "category_button")}
                            placeholder="New category name"
                            className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                          />
                          <button
                            name="category_button"
                            type="button"
                            onClick={handleAddCategory}
                            className="px-3 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                          >
                            Add
                          </button>
                          <button
                            type="button"
                            onClick={() => {
                              setShowAddCategory(false);
                              setNewCategory("");
                            }}
                            className="px-3 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500"
                          >
                            Cancel
                          </button>
                        </div>
                      )}
                    </div>

                    <div className="space-y-1">
                      <label className="flex items-center justify-between text-sm font-medium text-gray-700 dark:text-gray-300">
                        Supplier
                        <button
                          type="button"
                          onClick={() => setShowAddSupplier(true)}
                          className="text-indigo-600 hover:text-indigo-900 text-xs font-normal"
                        >
                          + Add New
                        </button>
                      </label>
                      <select
                        ref={supplierRef}
                        name="supplier"
                        value={formData.supplier}
                        onChange={handleChange}
                        onKeyDown={(e) => handleKeyDown(e, "unit_type")}
                        className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                      >
                        <option key="select-supplier-2" value="">Select Supplier</option>
                        {suppliers.map((supplier, index) => (
                          <option
                            key={supplier.supplier_id ?? supplier.supplier_name ?? index}
                            value={supplier.supplier_name}
                          >
                            {supplier.supplier_name}
                          </option>
                        ))}
                      </select>
                      {showAddSupplier && (
                        <div className="mt-2 space-y-2">
                          <input
                            type="text"
                            value={newSupplier}
                            onChange={(e) => setNewSupplier(e.target.value)}
                            placeholder="New supplier name"
                            onKeyDown={(e) => handleKeyDown(e, "contact")}
                            className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                          />
                          <input
                            type="text"
                            name="contact"
                            value={formData.contact}
                            onChange={(e) =>
                              setFormData((prev) => ({
                                ...prev,
                                contact: e.target.value,
                              }))
                            }
                            onKeyDown={(e) => handleKeyDown(e, "address")}
                            placeholder="Contact"
                            className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                          />
                          <input
                            type="text"
                            name="address"
                            value={formData.address}
                            onChange={(e) =>
                              setFormData((prev) => ({
                                ...prev,
                                address: e.target.value,
                              }))
                            }
                            onKeyDown={(e) => handleKeyDown(e, "supplier_button")}
                            placeholder="Address"
                            className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                          />
                          <div className="flex space-x-2">
                            <button
                              name="supplier_button"
                              type="button"
                              onClick={handleAddSupplier}
                              className="px-3 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                            >
                              Add
                            </button>
                            <button
                              type="button"
                              onClick={() => {
                                setShowAddSupplier(false);
                                setNewSupplier("");
                                setFormData((prev) => ({
                                  ...prev,
                                  contact: "",
                                  address: "",
                                }));
                              }}
                              className="px-3 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500"
                            >
                              Cancel
                            </button>
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="space-y-1">
                      <label className="flex items-center justify-between text-sm font-medium text-gray-700 dark:text-gray-300">
                        Unit Type
                        <button
                          type="button"
                          onClick={() => setShowAddUnitType(true)}
                          className="text-indigo-600 hover:text-indigo-900 text-xs font-normal"
                        >
                          + Add New
                        </button>
                      </label>
                      <select
                        ref={unitTypeRef}
                        name="unit_type"
                        value={formData.unit_type}
                        onChange={handleChange}
                        onKeyDown={(e) => handleKeyDown(e, "company")}
                        className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                      >
                        <option key="select-unit-type" value="">
                          Select Unit Type
                        </option>
                        {unitTypes.map((unit) => (
                          <option key={unit.id} value={unit.unit_name}>
                            {unit.unit_name}
                          </option>
                        ))}
                      </select>
                      {showAddUnitType && (
                        <div className="mt-2 flex space-x-2">
                          <input
                            type="text"
                            value={newUnitType}
                            onChange={(e) => setNewUnitType(e.target.value)}
                            onKeyDown={(e) => handleKeyDown(e, "unit_button")}
                            placeholder="New unit type name"
                            className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600"
                          />
                          <button
                            name="unit_button"
                            type="button"
                            onClick={handleAddUnitType}
                            className="px-3 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                          >
                            Add
                          </button>
                          <button
                            type="button"
                            onClick={() => {
                              setShowAddUnitType(false);
                              setNewUnitType("");
                            }}
                            className="px-3 py-2 bg-gray-300 dark:bg-gray-600 text-gray-900 dark:text-gray-300 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500"
                          >
                            Cancel
                          </button>
                        </div>
                      )}
                    </div>

                    <div className="space-y-1">
                      <label className="flex items-center justify-between text-sm font-medium text-gray-700 dark:text-gray-300">
                        Company
                        <button
                          type="button"
                          onClick={() => setShowAddCompany(true)}
                          className="text-indigo-600 hover:text-indigo-900 text-xs font-normal"
                        >
                          + Add New
                        </button>
                      </label>
                      <select
                        ref={companyRef}
                        name="company"
                        value={formData.company}
                        onChange={handleChange}
                        onKeyDown={(e) => handleKeyDown(e, "branch_name")}
                        className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                      >
                        <option key="select-company" value="">
                          Select Company
                        </option>
                        {companies.map((comp) => (
                          <option key={comp.id} value={comp.company_name}>
                            {comp.company_name}
                          </option>
                        ))}
                      </select>
                      {showAddCompany && (
                        <div className="mt-2 space-y-2 p-4 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700">
                          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Add New Company</h4>
                          <input
                            type="text"
                            value={newCompanyData.company_name}
                            onChange={(e) =>
                              setNewCompanyData((prev) => ({
                                ...prev,
                                company_name: e.target.value,
                              }))
                            }
                            placeholder="Company Name *"
                            className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                            required
                          />
                          <input
                            type="text"
                            value={newCompanyData.contact}
                            onChange={(e) => setNewCompanyData(prev => ({...prev, contact: e.target.value}))}
                            placeholder="Contact"
                            className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                          />
                          <input
                            type="text"
                            value={newCompanyData.address}
                            onChange={(e) => setNewCompanyData(prev => ({...prev, address: e.target.value}))}
                            placeholder="Address"
                            className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                          />
                          <input
                            type="number"
                            value={newCompanyData.opening_balance}
                            onChange={(e) => setNewCompanyData(prev => ({...prev, opening_balance: e.target.value}))}
                            placeholder="Opening Balance"
                            step="0.01"
                            className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                          />
                          <div className="flex space-x-2">
                            <button
                              name="company_button"
                              type="button"
                              onClick={handleAddCompany}
                              className="px-3 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                            >
                              Add Company
                            </button>
                            <button
                              type="button"
                              onClick={() => {
                                setShowAddCompany(false);
                                setNewCompanyData({
                                  company_name: "",
                                  contact: "",
                                  address: "",
                                  opening_balance: "",
                                });
                                setNewCompany("");
                              }}
                              className="px-3 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500"
                            >
                              Cancel
                            </button>
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Branch Name
                      </label>
                      <input
                        type="text"
                        name="branch_name"
                        value={formData.branch_name}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                        placeholder="Enter Branch Name"
                      />
                    </div>

                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Branch Quantity
                      </label>
                      <input
                        type="number"
                        name="branch_qty"
                        value={formData.branch_qty}
                        onChange={handleChange}
                        className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                        placeholder="Enter Branch Quantity"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Storage Information Section - Show for all product creation scenarios */}
                <div className="bg-white dark:bg-gray-900 p-8 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 max-w-full mx-auto">
                  <h3 className="text-2xl font-semibold text-center text-gray-800 dark:text-white mb-8">
                    Storage Information
                  </h3>
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                    <div className="space-y-1">
                      <label className="flex items-center justify-between text-sm font-medium text-gray-700 dark:text-gray-300">
                        Store Location
                        <button
                          type="button"
                          onClick={() => setShowAddStore(true)}
                          className="text-indigo-600 hover:text-indigo-900 text-xs font-normal"
                        >
                          + Add New
                        </button>
                      </label>
                      <select
                        ref={storeLocationRef}
                        name="store_location"
                        value={
                          productExists || isBatchOnly
                            ? batchFormData.store_location
                            : formData.store_location
                        }
                        onChange={
                          productExists || isBatchOnly
                            ? handleBatchChange
                            : handleChange
                        }
                        onKeyDown={(e) => handleKeyDown(e, "cabinet")}
                        className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                      >
                        <option key="select-store-location" value="">
                          Select Store Location
                        </option>
                        {stores.map((store) => (
                          <option key={store.id} value={store.store_name}>
                            {store.store_name}
                          </option>
                        ))}
                      </select>
                      {showAddStore && (
                        <div className="mt-2 space-y-2">
                          <input
                            type="text"
                            value={newStore}
                            onChange={(e) => setNewStore(e.target.value)}
                            placeholder="New store location name"
                            onKeyDown={(e) => handleKeyDown(e, "store_phone")}
                            className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                          />
                          <input
                            type="text"
                            name="phone_number"
                            value={formData.phone_number}
                            onChange={(e) =>
                              setFormData((prev) => ({
                                ...prev,
                                phone_number: e.target.value,
                              }))
                            }
                            onKeyDown={(e) => handleKeyDown(e, "store_address")}
                            placeholder="Phone Number"
                            className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                          />
                          <input
                            type="text"
                            name="address"
                            value={formData.address}
                            onChange={(e) =>
                              setFormData((prev) => ({
                                ...prev,
                                address: e.target.value,
                              }))
                            }
                            onKeyDown={(e) => handleKeyDown(e, "store_button")}
                            placeholder="Address"
                            className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                          />
                          <div className="flex space-x-2">
                            <button
                              name="store_button"
                              type="button"
                              onClick={handleAddStore}
                              className="px-3 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                            >
                              Add
                            </button>
                            <button
                              type="button"
                              onClick={() => {
                                setShowAddStore(false);
                                setNewStore("");
                                setFormData((prev) => ({
                                  ...prev,
                                  phone_number: "",
                                  address: "",
                                }));
                              }}
                              className="px-3 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500"
                            >
                              Cancel
                            </button>
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Cabinet
                      </label>
                      <input
                        ref={cabinetRef}
                        type="text"
                        name="cabinet"
                        value={
                          productExists || isBatchOnly
                            ? batchFormData.cabinet
                            : formData.cabinet
                        }
                        onChange={
                          productExists || isBatchOnly
                            ? handleBatchChange
                            : handleChange
                        }
                        onKeyDown={(e) => handleKeyDown(e, "row")}
                        className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                        placeholder="Enter cabinet location"
                      />
                    </div>

                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Row
                      </label>
                      <input
                        ref={rowRef}
                        type="text"
                        name="row"
                        value={
                          productExists || isBatchOnly
                            ? batchFormData.row
                            : formData.row
                        }
                        onChange={
                          productExists || isBatchOnly
                            ? handleBatchChange
                            : handleChange
                        }
                        onKeyDown={(e) => handleKeyDown(e, "submit_button")}
                        className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                        placeholder="Enter row location"
                      />
                    </div>
                  </div>
                </div>

              {/* Extra Fields Section - Show for create and edit product forms, not in batch-only mode */}
              {(!isBatchOnly && (initialData || !productExists)) && (
                <div className="bg-white dark:bg-gray-900 p-8 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 max-w-full mx-auto">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-2xl font-semibold text-center text-gray-800 dark:text-white">
                      Custom Fields
                    </h3>
                    <div className="flex space-x-2">
                      <button
                        type="button"
                        onClick={addExtraField}
                        className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors font-medium text-sm"
                      >
                        + Add Custom Field
                      </button>
                      {/* <button
                        type="button"
                        onClick={() => {
                          console.log("Current formData.extra_fields:", formData.extra_fields);
                          console.log("Current formData:", formData);
                          // Add a test field
                          setFormData(prev => ({
                            ...prev,
                            extra_fields: [...(prev.extra_fields || []), { name: "Test Field", value: "Test Value" }]
                          }));
                        }}
                        className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium text-sm"
                      >
                        Add Test Field
                      </button> */}
                    </div>
                  </div>
                  
                  {formData.extra_fields && formData.extra_fields.length > 0 ? (
                    <div className="space-y-4">
                      {formData.extra_fields.map((field, index) => (
                        <div key={index} className="flex items-center space-x-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                          {editingExtraFieldIndex === index ? (
                            // Edit mode
                            <>
                              <div className="flex-1">
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                  Field Name <span className="text-red-500">*</span>
                                </label>
                                <input
                                  type="text"
                                  value={editingExtraFieldData.name}
                                  onChange={(e) => handleExtraFieldDataChange('name', e.target.value)}
                                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                                  placeholder="Field name"
                                />
                              </div>
                              <div className="flex-1">
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                  Field Value
                                </label>
                                <input
                                  type="text"
                                  value={editingExtraFieldData.value}
                                  onChange={(e) => handleExtraFieldDataChange('value', e.target.value)}
                                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                                  placeholder="Field value"
                                />
                              </div>
                              <div className="flex space-x-2">
                                <button
                                  type="button"
                                  onClick={handleUpdateExtraField}
                                  className="px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm"
                                >
                                  Save
                                </button>
                                <button
                                  type="button"
                                  onClick={handleCancelExtraFieldEdit}
                                  className="px-3 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500 text-sm"
                                >
                                  Cancel
                                </button>
                              </div>
                            </>
                          ) : (
                            // View mode
                            <>
                              <div className="flex-1">
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                  {field.name}
                                </label>
                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                  {field.value || "No value"}
                                </p>
                              </div>
                              <div className="flex space-x-2">
                                <button
                                  type="button"
                                  onClick={() => handleEditExtraField(index)}
                                  className="px-3 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 text-sm"
                                >
                                  Edit
                                </button>
                                <button
                                  type="button"
                                  onClick={() => removeExtraField(index)}
                                  className="px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 text-sm"
                                >
                                  Delete
                                </button>
                              </div>
                            </>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                      <p>No custom fields added yet.</p>
                      <p className="text-sm mt-2">Click "Add Custom Field" to get started.</p>
                    </div>
                  )}
                </div>
              )}
              



              {/* Existing Variants Section - Show when editing a product with variants */}
              {initialData && existingVariants.length > 0 && (
                <div className="bg-white dark:bg-gray-900 p-6 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 max-w-full mx-auto">
                  {console.log('[DEBUG] Rendering variants section - editingVariantIndex:', editingVariantIndex, 'existingVariants length:', existingVariants.length)}
                  <h3 className="text-xl font-semibold text-center text-gray-800 dark:text-white mb-6">
                    Existing Variants/Batches
                  </h3>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700 text-xs">
                      <thead className="bg-gray-50 dark:bg-gray-800">
                        <tr>
                          <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Batch
                          </th>
                          <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Expiry
                          </th>
                          <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Cost
                          </th>
                          <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Price
                          </th>
                          <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            MRP
                          </th>
                          <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Barcode
                          </th>
                          <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Wholesale
                          </th>
                          <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Min Price
                          </th>
                          <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Min Stock
                          </th>
                          <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Stock Qty
                          </th>
                          <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                        {existingVariants.map((variant, index) => (
                          <tr key={variant.product_variant_id}>
                            {editingVariantIndex === index ? (
                              // Edit mode
                              <>
                                <td className="px-2 py-1 whitespace-nowrap">
                                  <input
                                    type="text"
                                    value={editingVariantData.batch_number || ""}
                                    onChange={(e) =>
                                      handleVariantDataChange("batch_number", e.target.value)
                                    }
                                    className="block w-full px-1 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded shadow-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                                    placeholder="Batch #"
                                  />
                                </td>
                                <td className="px-2 py-1 whitespace-nowrap">
                                  <input
                                    type="date"
                                    value={editingVariantData.expiry_date || ""}
                                    onChange={(e) =>
                                      handleVariantDataChange("expiry_date", e.target.value)
                                    }
                                    className="block w-full px-1 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded shadow-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                                  />
                                </td>
                                <td className="px-2 py-1 whitespace-nowrap">
                                  <input
                                    type="number"
                                    step="0.01"
                                    value={editingVariantData.buying_cost || ""}
                                    onChange={(e) =>
                                      handleVariantDataChange("buying_cost", e.target.value)
                                    }
                                    className="block w-full px-1 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded shadow-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                                    placeholder="0.00"
                                  />
                                </td>
                                <td className="px-2 py-1 whitespace-nowrap">
                                  <input
                                    type="number"
                                    step="0.01"
                                    value={editingVariantData.sales_price || ""}
                                    onChange={(e) =>
                                      handleVariantDataChange("sales_price", e.target.value)
                                    }
                                    className="block w-full px-1 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded shadow-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                                    placeholder="0.00"
                                  />
                                </td>
                                <td className="px-2 py-1 whitespace-nowrap">
                                  <input
                                    type="number"
                                    step="0.01"
                                    value={editingVariantData.mrp || ""}
                                    onChange={(e) =>
                                      handleVariantDataChange("mrp", e.target.value)
                                    }
                                    className="block w-full px-1 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded shadow-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                                    placeholder="0.00"
                                  />
                                </td>
                                <td className="px-2 py-1 whitespace-nowrap">
                                  <input
                                    type="text"
                                    value={editingVariantData.barcode || ""}
                                    onChange={(e) =>
                                      handleVariantDataChange("barcode", e.target.value)
                                    }
                                    className="block w-full px-1 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded shadow-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                                    placeholder="Barcode"
                                  />
                                </td>
                                <td className="px-2 py-1 whitespace-nowrap">
                                  <input
                                    type="number"
                                    step="0.01"
                                    value={editingVariantData.wholesale_price || ""}
                                    onChange={(e) =>
                                      handleVariantDataChange("wholesale_price", e.target.value)
                                    }
                                    className="block w-full px-1 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded shadow-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                                    placeholder="0.00"
                                  />
                                </td>
                                <td className="px-2 py-1 whitespace-nowrap">
                                  <input
                                    type="number"
                                    step="0.01"
                                    value={editingVariantData.minimum_price || ""}
                                    onChange={(e) =>
                                      handleVariantDataChange("minimum_price", e.target.value)
                                    }
                                    className="block w-full px-1 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded shadow-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                                    placeholder="0.00"
                                  />
                                </td>
                                <td className="px-2 py-1 whitespace-nowrap">
                                  <input
                                    type="number"
                                    step="0.01"
                                    value={editingVariantData.minimum_stock_quantity || ""}
                                    onChange={(e) =>
                                      handleVariantDataChange("minimum_stock_quantity", e.target.value)
                                    }
                                    className="block w-full px-1 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded shadow-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                                    placeholder="0"
                                  />
                                </td>
                                <td className="px-2 py-1 whitespace-nowrap">
                                  <input
                                    type="number"
                                    step="0.01"
                                    value={editingVariantData.opening_stock_quantity || ""}
                                    onChange={(e) =>
                                      handleVariantDataChange("opening_stock_quantity", e.target.value)
                                    }
                                    className="block w-full px-1 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded shadow-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                                    placeholder="0"
                                  />
                                </td>
                                <td className="px-2 py-1 whitespace-nowrap text-xs font-medium">
                                  <div className="flex space-x-1">
                                    <button
                                      type="button"
                                      onClick={handleUpdateVariant}
                                      className="text-xs text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 px-1 py-0.5 bg-indigo-50 dark:bg-indigo-900/20 rounded"
                                    >
                                      Update
                                    </button>
                                    <button
                                      type="button"
                                      onClick={handleCancelVariantEdit}
                                      className="text-xs text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300 px-1 py-0.5 bg-gray-50 dark:bg-gray-800 rounded"
                                    >
                                      Cancel
                                    </button>
                                  </div>
                                </td>
                              </>
                            ) : (
                              // View mode
                              <>
                                <td className="px-2 py-1 whitespace-nowrap text-xs text-gray-900 dark:text-gray-100">
                                  {variant.batch_number || "N/A"}
                                </td>
                                <td className="px-2 py-1 whitespace-nowrap text-xs text-gray-900 dark:text-gray-100">
                                  {variant.expiry_date ? new Date(variant.expiry_date).toLocaleDateString() : "N/A"}
                                </td>
                                <td className="px-2 py-1 whitespace-nowrap text-xs text-gray-900 dark:text-gray-100">
                                  {variant.buying_cost ? `LKR${parseFloat(variant.buying_cost).toFixed(2)}` : "N/A"}
                                </td>
                                <td className="px-2 py-1 whitespace-nowrap text-xs text-gray-900 dark:text-gray-100">
                                  {variant.sales_price ? `LKR${parseFloat(variant.sales_price).toFixed(2)}` : "N/A"}
                                </td>
                                <td className="px-2 py-1 whitespace-nowrap text-xs text-gray-900 dark:text-gray-100">
                                  {variant.mrp ? `LKR${parseFloat(variant.mrp).toFixed(2)}` : "N/A"}
                                </td>
                                <td className="px-2 py-1 whitespace-nowrap text-xs text-gray-900 dark:text-gray-100">
                                  {variant.barcode || "N/A"}
                                </td>
                                <td className="px-2 py-1 whitespace-nowrap text-xs text-gray-900 dark:text-gray-100">
                                  {variant.wholesale_price ? `LKR${parseFloat(variant.wholesale_price).toFixed(2)}` : "N/A"}
                                </td>
                                <td className="px-2 py-1 whitespace-nowrap text-xs text-gray-900 dark:text-gray-100">
                                  {variant.minimum_price ? `LKR${parseFloat(variant.minimum_price).toFixed(2)}` : "N/A"}
                                </td>
                                <td className="px-2 py-1 whitespace-nowrap text-xs text-gray-900 dark:text-gray-100">
                                  {variant.minimum_stock_quantity || "N/A"}
                                </td>
                                <td className="px-2 py-1 whitespace-nowrap text-xs text-gray-900 dark:text-gray-100">
                                  {variant.opening_stock_quantity || "N/A"}
                                </td>
                                <td className="px-2 py-1 whitespace-nowrap text-xs font-medium">
                                  <div className="flex space-x-1">
                                    <button
                                      type="button"
                                      onClick={(e) => {
                                        e.preventDefault();
                                        e.stopPropagation();
                                        console.log('[DEBUG] Edit button clicked for index:', index);
                                        console.log('[DEBUG] Edit button clicked - existingVariants:', existingVariants);
                                        console.log('[DEBUG] Edit button clicked - variant at index:', existingVariants[index]);
                                        console.log('[DEBUG] Edit button clicked - current editingVariantIndex before:', editingVariantIndex);
                                        console.log('[DEBUG] Edit button clicked - calling handleEditVariant with index:', index);
                                        handleEditVariant(index);
                                        console.log('[DEBUG] Edit button clicked - handleEditVariant called for index:', index);
                                        console.log('[DEBUG] Edit button clicked - editingVariantIndex after call:', editingVariantIndex);
                                      }}
                                      className="text-xs text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 px-1 py-0.5 bg-indigo-50 dark:bg-indigo-900/20 rounded"
                                    >
                                      Edit
                                    </button>
                                    <button
                                      type="button"
                                      onClick={() => handleDeleteVariant(index)}
                                      className="text-xs text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 px-1 py-0.5 bg-red-50 dark:bg-red-900/20 rounded"
                                    >
                                      Delete
                                    </button>
                                  </div>
                                </td>
                              </>
                            )}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>
            
            {/* Form Action Buttons - Show for both add and edit forms */}
            <div className="mt-8 flex justify-between items-center pb-8">
              {/* Show Add Custom Field button only for create and edit product forms, not in batch-only or add batch mode */}
              {(!isBatchOnly && (initialData || !productExists)) && (
                <button
                  type="button"
                  onClick={addExtraField}
                  className="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors font-medium"
                >
                  Add Custom Field
                </button>
              )}
              {initialData ? (
                // Edit form buttons
                <div className="flex space-x-4">
                  <button
                    type="button"
                    onClick={onClose}
                    className="px-6 py-3 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors font-medium"
                  >
                    Cancel
                  </button>
                  <button
                    type={editingVariantIndex !== -1 ? "button" : "submit"}
                    ref={submitButtonRef}
                    onClick={async (e) => {
                      if (editingVariantIndex !== -1) {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log("[DEBUG] Variant update button clicked");
                        console.log("[DEBUG] Submit button - editingVariantIndex:", editingVariantIndex);
                        console.log("[DEBUG] Submit button - editingVariantData:", editingVariantData);
                        await handleUpdateVariant();
                        console.log("[DEBUG] Variant update completed");
                        return false;
                      }
                    }}
                    className="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors font-medium"
                  >
                    {editingVariantIndex !== -1 ? "Update Variant" : "Update Product"}
                  </button>
                </div>
              ) : (
                // Add form buttons
                <div className="flex space-x-4">
                  <button
                    type="button"
                    onClick={onClose}
                    className="px-6 py-3 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors font-medium"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    ref={submitButtonRef}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        e.preventDefault();
                        // Submit the form when Enter is pressed on the submit button
                        e.target.click();
                      }
                    }}
                    className="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors font-medium"
                  >
                    {productExists || isBatchOnly ? "Add Batch" : "Create Product"}
                  </button>
                </div>
              )}
            </div>
          </div>
        </form>
      </div>
      {showBatchConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200">
              Product '{formData.product_name}' already exists.
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
              Do you want to add a new batch with number '
              {formData.batch_number}'?
            </p>
            <div className="mt-4 flex justify-end space-x-3">
              <button
                type="button"
                onClick={handleCancelBatch}
                className="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleConfirmBatch}
                className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
              >
                Add Batch
              </button>
            </div>
          </div>
        </div>
      )}
      {showProductConfirm && selectedProductForBatch && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg max-w-md">
            <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200">
              Confirm Product for Batch
            </h3>
            <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-md">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                <strong>Product Name:</strong>{" "}
                {selectedProductForBatch.product_name}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                <strong>Product ID:</strong>{" "}
                {selectedProductForBatch.product_id}
              </p>
              {pendingBatchData?.batch_number && (
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  <strong>Batch Number:</strong> {pendingBatchData.batch_number}
                </p>
              )}
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-4">
              Are you sure you want to add this batch to the above product?
            </p>
            <div className="mt-6 flex justify-end space-x-3">
              <button
                type="button"
                onClick={handleCancelProductConfirm}
                className="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleConfirmProductForBatch}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Confirm & Add Batch
              </button>
            </div>
          </div>
        </div>
      )}
      {showCloseConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
            <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200">
              Unsaved Changes
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
              Are you sure you want to close? Any unsaved changes will be lost.
            </p>
            <div className="mt-4 flex justify-end space-x-3">
              <button
                type="button"
                onClick={handleCancelClose}
                className="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleConfirmClose}
                className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
      {showCustomFieldForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200 mb-4">
              Add Custom Field
            </h3>
            <div className="space-y-4">
              {newCustomField.map((field, idx) => (
                <div key={idx} className="flex space-x-2 items-end mb-2">
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Field Name <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={field.name}
                      onChange={e => {
                        const updated = [...newCustomField];
                        updated[idx].name = e.target.value;
                        setNewCustomField(updated);
                      }}
                      className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                      placeholder="Enter field name"
                      autoFocus={idx === 0}
                    />
                  </div>
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Field Value
                    </label>
                    <input
                      type="text"
                      value={field.value}
                      onChange={e => {
                        const updated = [...newCustomField];
                        updated[idx].value = e.target.value;
                        setNewCustomField(updated);
                      }}
                      className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                      placeholder="Enter field value"
                    />
                  </div>
                  <button
                    type="button"
                    className="mb-1 px-2 py-1 bg-green-500 text-white rounded hover:bg-green-600 text-lg font-bold"
                    onClick={() => {
                      setNewCustomField([...newCustomField, { name: '', value: '' }]);
                    }}
                    title="Add another field"
                  >
                    +
                  </button>
                  {newCustomField.length > 1 && (
                    <button
                      type="button"
                      className="mb-1 px-2 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-lg font-bold"
                      onClick={() => {
                        setNewCustomField(newCustomField.filter((_, i) => i !== idx));
                      }}
                      title="Remove this field"
                    >
                      –
                    </button>
                  )}
                </div>
              ))}
            </div>
            <div className="mt-6 flex justify-end space-x-3">
              <button
                type="button"
                onClick={handleCustomFieldCancel}
                className="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={() => {
                  // Validate all fields
                  for (const field of newCustomField) {
                    if (!field.name.trim()) {
                      toast.error("Field name is required");
                      return;
                    }
                  }
                  setFormData(prevData => ({
                    ...prevData,
                    extra_fields: [
                      ...prevData.extra_fields,
                      ...newCustomField.filter(f => f.name.trim())
                    ]
                  }));
                  setNewCustomField([{ name: '', value: '' }]);
                  setShowCustomFieldForm(false);
                  toast.success("Custom field(s) added successfully");
                }}
                className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}
      {showBarcodeVariants && barcodeVariants.length > 0 && (
        <div className="mt-2 p-2 border rounded bg-gray-50 dark:bg-gray-800">
          <div className="font-semibold mb-1">Select Batch for Barcode:</div>
          {barcodeVariants.map(variant => (
            <div key={variant.product_variant_id} className="flex items-center justify-between p-1 border-b">
              <span>
                Batch: <b>{variant.batch_number}</b> | Expiry: <b>{variant.expiry_date}</b> | Stock: <b>{variant.closing_stock_quantity}</b>
              </span>
              <button
                className="ml-2 px-2 py-1 bg-indigo-600 text-white rounded"
                onClick={() => {
                  setFormData(prev => ({
                    ...prev,
                    batch_number: variant.batch_number,
                    expiry_date: variant.expiry_date,
                    buying_cost: variant.buying_cost,
                    sales_price: variant.sales_price,
                    mrp: variant.mrp,
                    barcode: variant.barcode,
                    // ...add other fields as needed
                  }));
                  setShowBarcodeVariants(false);
                }}
              >
                Select
              </button>
            </div>
          ))}
        </div>
      )}
      <ToastContainer />
    </>
  );
};

export default ItemForm;
