<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Picqer\Barcode\BarcodeGeneratorPNG;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    protected $primaryKey = 'product_id'; // Specify the primary key

    protected $fillable = [
        'product_name', 'item_code',
        'closing_stock_quantity',
        'category', 'supplier', 'unit_type',
        'store_location', 'cabinet', 'row', // Added missing location fields
        'company', 'branch_name', 'branch_qty',
        'extra_fields'
        // Removed 'minimum_stock_quantity' - it was moved to product_variants table
    ];

    protected $casts = [
        'closing_stock_quantity' => 'float',
        'extra_fields' => 'array',
        // Removed 'minimum_stock_quantity' - it was moved to product_variants table
    ];

    protected $appends = [
        'buying_cost',
        'sales_price',
        'opening_stock_quantity',
        'opening_stock_value',
        'mrp',
        'minimum_price',
        'wholesale_price',
        'barcode',
        'batch_number',
        'expiry_date',
        'store_location',
        'cabinet',
        'row',
        'extra_fields',
        'minimum_stock_quantity',
    ];

    // DEPRECATED: Do not use this method - opening stock should NEVER be modified
    // Stock tracking is now handled through transaction records (sales, purchases)
    // Opening stock is a FIXED baseline value that never changes
    public function updateStock($quantity, $action = 'subtract')
    {
        // DO NOTHING - opening stock should never be modified
        // This method is kept for backward compatibility but does nothing
        Log::warning('Deprecated updateStock method called - opening stock should never be modified', [
            'product_id' => $this->product_id,
            'quantity' => $quantity,
            'action' => $action
        ]);
    }

    // Method to check if stock is below the minimum threshold
    public function isLowStock()
    {
        return $this->opening_stock_quantity < $this->minimum_stock_quantity;
    }

      public function generateBarcode()
    {
        $generator = new BarcodeGeneratorPNG();
        return $generator->getBarcode($this->barcode, $generator::TYPE_CODE_128);
    }

    public function variants()
    {
        return $this->hasMany(ProductVariant::class, 'product_id', 'product_id');
    }

    public function getBuyingCostAttribute()
    {
        return $this->variants && $this->variants->count() > 0 ? $this->variants->first()->buying_cost : 0;
    }

    public function getSalesPriceAttribute()
    {
        return $this->variants && $this->variants->count() > 0 ? $this->variants->first()->sales_price : 0;
    }

    public function getOpeningStockQuantityAttribute()
    {
        return $this->variants && $this->variants->count() > 0 ? $this->variants->first()->opening_stock_quantity : 0;
    }

    public function getOpeningStockValueAttribute()
    {
        if ($this->variants && $this->variants->count() > 0) {
            $variant = $this->variants->first();
            return $variant->opening_stock_quantity * $variant->buying_cost;
        }
        return 0;
    }

    public function getMrpAttribute()
    {
        return $this->variants && $this->variants->count() > 0 ? $this->variants->first()->mrp : 0;
    }

    public function getMinimumPriceAttribute()
    {
        return $this->variants && $this->variants->count() > 0 ? $this->variants->first()->minimum_price : 0;
    }

    public function getWholesalePriceAttribute()
    {
        return $this->variants && $this->variants->count() > 0 ? $this->variants->first()->wholesale_price : 0;
    }

    public function getBarcodeAttribute()
    {
        return $this->variants && $this->variants->count() > 0 ? $this->variants->first()->barcode : '';
    }

    public function getBatchNumberAttribute()
    {
        return $this->variants && $this->variants->count() > 0 ? $this->variants->first()->batch_number : '';
    }

    public function getExpiryDateAttribute()
    {
        return $this->variants && $this->variants->count() > 0 ? $this->variants->first()->expiry_date : null;
    }

    public function getStoreLocationAttribute()
    {
        return $this->variants && $this->variants->count() > 0 ? $this->variants->first()->store_location : '';
    }

    public function getCabinetAttribute()
    {
        return $this->variants && $this->variants->count() > 0 ? $this->variants->first()->cabinet : '';
    }

    public function getRowAttribute()
    {
        return $this->variants && $this->variants->count() > 0 ? $this->variants->first()->row : '';
    }

    public function getExtraFieldsAttribute()
    {
        // First check if the product has its own extra_fields
        if ($this->attributes['extra_fields'] ?? null) {
            \Log::info("Product extra_fields found in attributes:", ['extra_fields' => $this->attributes['extra_fields']]);
            return $this->attributes['extra_fields'];
        }
        
        // If not, check the first variant's extra_fields
        if ($this->variants && $this->variants->count() > 0) {
            $variantExtraFields = $this->variants->first()->extra_fields;
            \Log::info("Product extra_fields found in first variant:", ['extra_fields' => $variantExtraFields]);
            return $variantExtraFields;
        }
        
        \Log::info("No extra_fields found in product or variants");
        return null;
    }

    public function getMinimumStockQuantityAttribute()
    {
        return $this->variants && $this->variants->count() > 0 ? $this->variants->first()->minimum_stock_quantity : 0;
    }

    public function deletedByUser()
    {
        return $this->belongsTo(User::class, 'deleted_by')->withTrashed();
    }

    protected static function boot()
    {
        parent::boot();
        
        // When creating a new product, set closing_stock_quantity to opening_stock_quantity
        static::creating(function ($model) {
            if (isset($model->product_id)) {
                unset($model->product_id); // Ignore product_id on create
            }
            
            // Set closing_stock_quantity to opening_stock_quantity if not already set
            if (!isset($model->closing_stock_quantity) || is_null($model->closing_stock_quantity)) {
                $model->closing_stock_quantity = $model->opening_stock_quantity ?? 0;
            }
        });
        
        // When a product is created, also update closing_stock_quantity for all its variants
        static::created(function ($model) {
            // This will be handled by the ProductVariant model's boot method
        });
    }
}
