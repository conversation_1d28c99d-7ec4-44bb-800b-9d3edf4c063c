<?php

namespace App\Http\Controllers;

use App\Models\ProductVariant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;

class ProductVariantController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        try {
            $variants = ProductVariant::with('product')->get();
            return response()->json([
                'message' => 'Product variants fetched successfully',
                'data' => $variants
            ], 200);
        } catch (\Exception $e) {
            Log::error('Error fetching product variants: ' . $e->getMessage());
            return response()->json([
                'message' => 'Error fetching product variants',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'product_id' => 'required|exists:products,product_id',
                'batch_number' => 'required|string',
                'expiry_date' => 'nullable|date',
                'buying_cost' => 'nullable|numeric|min:0',
                'sales_price' => 'required|numeric|min:0',
                'minimum_price' => 'nullable|numeric|min:0',
                'wholesale_price' => 'nullable|numeric|min:0',
                'barcode' => 'nullable|string',
                'mrp' => 'required|numeric|min:0',
                'minimum_stock_quantity' => 'nullable|numeric|min:0',
                'opening_stock_quantity' => 'nullable|numeric|min:0',
                'opening_stock_value' => 'nullable|numeric|min:0',
                'store_location' => 'nullable|string',
                'cabinet' => 'nullable|string',
                'row' => 'nullable|string',
                'extra_fields' => 'nullable|array',
            ]);

            // Generate barcode if not provided
            if (empty($validatedData['barcode'])) {
                $validatedData['barcode'] = 'BAR' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
                while (ProductVariant::where('barcode', $validatedData['barcode'])->exists()) {
                    $validatedData['barcode'] = 'BAR' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
                }
            }

            $variant = ProductVariant::create($validatedData);

            return response()->json([
                'message' => 'Product variant created successfully',
                'data' => $variant
            ], 201);
        } catch (\Exception $e) {
            Log::error('Error creating product variant: ' . $e->getMessage());
            return response()->json([
                'message' => 'Error creating product variant',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        try {
            $variant = ProductVariant::with('product')->findOrFail($id);
            return response()->json([
                'message' => 'Product variant fetched successfully',
                'data' => $variant
            ], 200);
        } catch (\Exception $e) {
            Log::error('Error fetching product variant: ' . $e->getMessage());
            return response()->json([
                'message' => 'Product variant not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        Log::info("=== PRODUCT VARIANT UPDATE START ===");
        Log::info("Updating product variant ID: $id", $request->all());
        try {
            $variant = ProductVariant::findOrFail($id);
            Log::info("Found variant to update:", [
                'variant_id' => $variant->product_variant_id,
                'product_id' => $variant->product_id,
                'batch_number' => $variant->batch_number,
                'current_data' => $variant->toArray()
            ]);
            
            // Check if this is the first variant for this product
            $allVariantsForProduct = ProductVariant::where('product_id', $variant->product_id)->orderBy('product_variant_id')->get();
            $isFirstVariant = $allVariantsForProduct->first()->product_variant_id === $variant->product_variant_id;
            
            Log::info("Variant position check:", [
                'variant_id' => $variant->product_variant_id,
                'product_id' => $variant->product_id,
                'total_variants_for_product' => $allVariantsForProduct->count(),
                'is_first_variant' => $isFirstVariant,
                'all_variant_ids' => $allVariantsForProduct->pluck('product_variant_id')->toArray()
            ]);
            
            $validatedData = $request->validate([
                'batch_number' => 'sometimes|required|string',
                'expiry_date' => 'nullable|date',
                'buying_cost' => 'nullable|numeric|min:0',
                'sales_price' => 'sometimes|required|numeric|min:0',
                'minimum_price' => 'nullable|numeric|min:0',
                'wholesale_price' => 'nullable|numeric|min:0',
                'barcode' => 'nullable|string',
                'mrp' => 'sometimes|required|numeric|min:0',
                'minimum_stock_quantity' => 'nullable|numeric|min:0',
                'opening_stock_quantity' => 'nullable|numeric|min:0',
                'opening_stock_value' => 'nullable|numeric|min:0',
                'store_location' => 'nullable|string',
                'cabinet' => 'nullable|string',
                'row' => 'nullable|string',
                'extra_fields' => 'nullable|array',
            ]);

            Log::info("Validated data for update:", $validatedData);

            // Check for unique constraint violation on product_id + batch_number
            if (isset($validatedData['batch_number'])) {
                Log::info("Checking batch number uniqueness:", [
                    'product_id' => $variant->product_id,
                    'current_batch_number' => $variant->batch_number,
                    'new_batch_number' => $validatedData['batch_number'],
                    'variant_id' => $variant->product_variant_id
                ]);
                
                // Handle NULL batch numbers properly
                $batchNumberToCheck = $validatedData['batch_number'];
                if (empty($batchNumberToCheck)) {
                    $batchNumberToCheck = null;
                }
                
                $existingVariant = ProductVariant::where('product_id', $variant->product_id)
                    ->where('batch_number', $batchNumberToCheck)
                    ->where('product_variant_id', '!=', $id)
                    ->first();
                
                if ($existingVariant) {
                    Log::warning("Batch number already exists for this product", [
                        'product_id' => $variant->product_id,
                        'batch_number' => $validatedData['batch_number'],
                        'existing_variant_id' => $existingVariant->product_variant_id,
                        'current_variant_id' => $variant->product_variant_id
                    ]);
                    return response()->json([
                        'message' => 'Batch number already exists for this product',
                        'error' => 'A variant with this batch number already exists for this product'
                    ], 422);
                } else {
                    Log::info("Batch number uniqueness check passed", [
                        'product_id' => $variant->product_id,
                        'batch_number' => $validatedData['batch_number']
                    ]);
                }
            } else {
                Log::info("No batch number provided in update data");
            }

            // Check for foreign key constraints before update
            Log::info("Checking foreign key constraints for variant ID: $id");
            
            // Check sale_items
            $saleItemsCount = \DB::table('sale_items')->where('product_variant_id', $id)->count();
            Log::info("Sale items referencing this variant: $saleItemsCount");
            
            // Check purchase_items
            $purchaseItemsCount = \DB::table('purchase_items')->where('product_variant_id', $id)->count();
            Log::info("Purchase items referencing this variant: $purchaseItemsCount");
            
            // Check invoice_items
            $invoiceItemsCount = \DB::table('invoice_items')->where('product_variant_id', $id)->count();
            Log::info("Invoice items referencing this variant: $invoiceItemsCount");
            
            // Check sales_return_items
            $salesReturnItemsCount = \DB::table('sales_return_items')->where('product_variant_id', $id)->count();
            Log::info("Sales return items referencing this variant: $salesReturnItemsCount");
            
            // Check purchase_return_items
            $purchaseReturnItemsCount = \DB::table('purchase_return_items')->where('product_variant_id', $id)->count();
            Log::info("Purchase return items referencing this variant: $purchaseReturnItemsCount");
            
            // Check stock_rechecks
            $stockRechecksCount = \DB::table('stock_rechecks')->where('product_variant_id', $id)->count();
            Log::info("Stock rechecks referencing this variant: $stockRechecksCount");

            // Use database transaction to ensure data consistency
            DB::beginTransaction();
            try {
                Log::info("Starting variant update with data:", $validatedData);
                $variant->update($validatedData);
                DB::commit();
                
                Log::info("Variant updated successfully", [
                    'variant_id' => $variant->product_variant_id,
                    'updated_data' => $variant->fresh()->toArray()
                ]);
                
                Log::info("=== PRODUCT VARIANT UPDATE COMPLETE ===");
                return response()->json([
                    'message' => 'Product variant updated successfully',
                    'data' => $variant->fresh()
                ], 200);
            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('Database error updating product variant: ' . $e->getMessage(), [
                    'variant_id' => $id,
                    'data' => $validatedData,
                    'error' => $e->getMessage(),
                    'error_code' => $e->getCode(),
                    'trace' => $e->getTraceAsString()
                ]);
                
                // Check if it's a unique constraint violation
                if (str_contains($e->getMessage(), 'Duplicate entry') || str_contains($e->getMessage(), 'UNIQUE constraint failed')) {
                    return response()->json([
                        'message' => 'Batch number already exists for this product',
                        'error' => 'A variant with this batch number already exists for this product'
                    ], 422);
                }
                
                throw $e;
            }
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::warning('Validation error updating product variant: ' . $e->getMessage(), [
                'variant_id' => $id,
                'errors' => $e->errors()
            ]);
            return response()->json([
                'message' => 'Validation error',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Error updating product variant: ' . $e->getMessage(), [
                'variant_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'message' => 'Error updating product variant',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, $id)
    {
        Log::info("Deleting product variant ID: $id");
        try {
            $variant = ProductVariant::findOrFail($id);
            
            // Get user ID with fallback and log
            $userId = $request->user()->id ?? auth()->id();
            if (!$userId) {
                Log::warning('No user ID found when deleting product variant', [
                    'request_user' => $request->user(),
                    'auth_id' => auth()->id(),
                ]);
                return response()->json(['message' => 'No authenticated or provided user found for delete action.'], 403);
            }

            // Check for transaction history before deletion
            DB::beginTransaction();

            // Check if variant is referenced in purchase_items
            $purchaseItemsCount = DB::table('purchase_items')->where('product_variant_id', $id)->count();
            if ($purchaseItemsCount > 0) {
                return response()->json([
                    'message' => 'Cannot delete batch. It is referenced in ' . $purchaseItemsCount . ' purchase(s). Please remove it from purchases first.'
                ], 409);
            }

            // Check if variant is referenced in invoice_items (Sales Invoices)
            $invoiceItemsCount = DB::table('invoice_items')->where('product_variant_id', $id)->count();
            if ($invoiceItemsCount > 0) {
                return response()->json([
                    'message' => 'Cannot delete batch. It is referenced in ' . $invoiceItemsCount . ' sales invoice(s). Please remove it from sales invoices first.'
                ], 409);
            }

            // Check if variant is referenced in sale_items (POS Sales)
            $saleItemsCount = DB::table('sale_items')->where('product_variant_id', $id)->count();
            if ($saleItemsCount > 0) {
                return response()->json([
                    'message' => 'Cannot delete batch. It is referenced in ' . $saleItemsCount . ' POS sale(s). Please remove it from POS sales first.'
                ], 409);
            }

            // Check if variant is referenced in sales_return_items
            $salesReturnItemsCount = DB::table('sales_return_items')->where('product_variant_id', $id)->count();
            if ($salesReturnItemsCount > 0) {
                return response()->json([
                    'message' => 'Cannot delete batch. It is referenced in ' . $salesReturnItemsCount . ' sales return(s). Please remove it from sales returns first.'
                ], 409);
            }

            // Check if variant is referenced in purchase_return_items
            $purchaseReturnItemsCount = DB::table('purchase_return_items')->where('product_variant_id', $id)->count();
            if ($purchaseReturnItemsCount > 0) {
                return response()->json([
                    'message' => 'Cannot delete batch. It is referenced in ' . $purchaseReturnItemsCount . ' purchase return(s). Please remove it from purchase returns first.'
                ], 409);
            }

            // Check if variant is referenced in stock_rechecks
            $stockRechecksCount = DB::table('stock_rechecks')->where('product_variant_id', $id)->count();
            if ($stockRechecksCount > 0) {
                return response()->json([
                    'message' => 'Cannot delete batch. It is referenced in ' . $stockRechecksCount . ' stock recheck(s). Please remove it from stock rechecks first.'
                ], 409);
            }

            // If variant has any transaction history, soft delete it
            $hasTransactionHistory = $purchaseItemsCount > 0 || $invoiceItemsCount > 0 || $saleItemsCount > 0 || 
                                   $salesReturnItemsCount > 0 || $purchaseReturnItemsCount > 0 || $stockRechecksCount > 0;

            if ($hasTransactionHistory) {
                // Soft delete the variant
                $variant->deleted_by = $userId;
                $variant->save();
                $variant->delete(); // Soft delete

                DB::commit();

                return response()->json([
                    'message' => 'Batch has transaction history and has been soft deleted. It can be restored from the recycle bin if needed.',
                    'soft_deleted' => true
                ], 200);
            }

            // If no transaction history, soft delete (for consistency)
            $variant->deleted_by = $userId;
            $variant->save();
            $variant->delete(); // Soft delete

            DB::commit();

            return response()->json([
                'message' => 'Product variant deleted successfully'
            ], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error deleting product variant: ' . $e->getMessage());
            return response()->json([
                'message' => 'Error deleting product variant',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getDeletedProductVariants()
    {
        $variants = ProductVariant::onlyTrashed()->with(['product', 'deletedByUser'])->get();
        return response()->json(['data' => $variants->map(function($variant) {
            return array_merge($variant->toArray(), [
                'deleted_by_user' => $variant->deletedByUser ? [
                    'id' => $variant->deletedByUser->id,
                    'name' => $variant->deletedByUser->name,
                    'email' => $variant->deletedByUser->email,
                ] : null,
            ]);
        })]);
    }

    public function restoreProductVariant($id)
    {
        $variant = ProductVariant::onlyTrashed()->findOrFail($id);
        $variant->restore();
        return response()->json($variant->load('product'));
    }

    public function forceDeleteProductVariant($id)
    {
        $variant = ProductVariant::onlyTrashed()->findOrFail($id);
        $variant->forceDelete();
        return response()->noContent();
    }

    /**
     * Search product variants by barcode, returning all variants (with different batch numbers) that share the same barcode.
     */
    public function searchByBarcode(Request $request)
    {
        $barcode = $request->input('barcode');
        $variants = \App\Models\ProductVariant::where('barcode', $barcode)->with('product')->get();
        return response()->json($variants);
    }
}
