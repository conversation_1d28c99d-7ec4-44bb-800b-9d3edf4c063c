<?php

namespace App\Http\Controllers;

use App\Models\InvoiceItem;
use App\Models\PurchaseItem;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\SaleItem;
use App\Models\StockRecheck;
use App\Services\ClosingStockService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class StockReportController extends Controller
{
    /**
     * Item-wise stock report for /api/stock-reports
     */
    public function index(Request $request)
    {
        // For now, just call detailedReport logic (or you can customize as needed)
        // Option 1: Call detailedReport directly (if logic is compatible)
        // return $this->detailedReport($request);

        // Option 2: Minimal implementation for item-wise stock
        try {
            $query = Product::query();

            // Apply filters
            if ($request->has('barcode') && $request->barcode !== '') {
                $query->whereHas('variants', function($q) use ($request) {
                    $q->where('barcode', 'like', '%' . $request->barcode . '%');
                });
            }
            if ($request->has('itemName') && $request->itemName !== '') {
                $query->where('product_name', 'like', '%' . $request->itemName . '%');
            }
            if ($request->has('category') && $request->category !== '') {
                $query->where('category', 'like', '%' . $request->category . '%');
            }
            if ($request->has('supplier') && $request->supplier !== '') {
                $query->where('supplier', 'like', '%' . $request->supplier . '%');
            }

            $products = $query->with('variants')->get();

            $stockReports = $products->map(function ($product) {
                // Sum up all variant stocks if variants exist
                $stockQuantity = 0;
                $stockValue = 0;
                $batches = [];
                if ($product->variants && $product->variants->count() > 0) {
                    foreach ($product->variants as $variant) {
                        // Calculate actual closing stock
                        $closingStock = $this->calculateVariantClosingStock($variant);
                        $openingStock = $variant->opening_stock_quantity ?? 0;
                        $price = $variant->sales_price ?? $variant->mrp ?? 0;
                        
                        $stockQuantity += $closingStock;
                        $stockValue += $closingStock * $price;
                        $batches[] = [
                            'batchNumber' => $variant->batch_number ?? 'N/A',
                            'expiryDate' => $variant->expiry_date ?? null,
                            'openingQuantity' => $openingStock,
                            'closingQuantity' => $closingStock,
                            'quantity' => $closingStock, // Use closing stock as current quantity
                            'unitPrice' => $price,
                            'mrp' => $variant->mrp ?? 0,
                            'salesPrice' => $variant->sales_price ?? 0,
                        ];
                    }
                } else {
                    $stockQuantity = $product->opening_stock_quantity ?? 0;
                    $stockValue = $stockQuantity * ($product->sales_price ?? 0);
                }
                return [
                    'itemCode' => $product->item_code,
                    'itemName' => $product->product_name,
                    'category' => $product->category,
                    'supplier' => $product->supplier,
                    'stockQuantity' => $stockQuantity,
                    'stockValue' => $stockValue,
                    'batches' => $batches,
                    'hasBatches' => count($batches) > 0,
                    'barcodes' => $product->variants->pluck('barcode')->filter()->toArray(),
                ];
            });

            return response()->json($stockReports, 200);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to fetch stock reports: ' . $e->getMessage()], 500);
        }
    }

    public function detailedReport(Request $request)
    {
        Log::info('Fetching detailed stock report with filters:', $request->all());

        try {
            $query = Product::with('variants');

            // Apply filters
            if ($request->has('barcode') && $request->barcode !== '') {
                $query->whereHas('variants', function($q) use ($request) {
                    $q->where('barcode', 'like', '%' . $request->barcode . '%');
                });
            }

            if ($request->has('itemName') && $request->itemName !== '') {
                $query->where('product_name', 'like', '%' . $request->itemName . '%');
            }

            if ($request->has('category') && $request->category !== '') {
                $query->where('category', 'like', '%' . $request->category . '%');
            }

            if ($request->has('supplier') && $request->supplier !== '') {
                $query->where('supplier', 'like', '%' . $request->supplier . '%');
            }

            if ($request->has('location') && $request->location !== '') {
                $query->where('store_location', 'like', '%' . $request->location . '%');
            }

            // Fetch products with variants
            $products = $query->get();

            Log::info('Products fetched:', $products->toArray());

            // Get the date range from the request (for transaction history display only)
            $fromDate = $request->input('fromDate');
            $toDate = $request->input('toDate');

            // After fetching $products:
            $filterBatchNumber = $request->input('batchNumber');
            $filterVariantId = $request->input('variant_id');

            // Prepare the detailed stock report data using stored closing stock values
            $stockReports = $products->map(function ($product) use ($fromDate, $toDate, $filterBatchNumber, $filterVariantId) {
                $variants = $product->variants ?? collect([]);
                $hasBatches = $variants->isNotEmpty();

                if ($hasBatches) {
                    // For products with variants, use stored closing stock values
                    $batchData = $variants->map(function ($variant) use ($fromDate, $toDate, $filterBatchNumber, $filterVariantId) {
                        if (isset($filterBatchNumber) && $filterBatchNumber !== '' && $variant->batch_number != $filterBatchNumber) {
                            return null;
                        }
                        if (isset($filterVariantId) && $filterVariantId !== '' && $variant->product_variant_id != $filterVariantId) {
                            return null;
                        }

                        // Get transaction history for display purposes (always calculate)
                        $purchasedForReportPeriod = PurchaseItem::where('product_variant_id', $variant->product_variant_id)
                            ->sum('quantity') ?? 0;

                        $soldInvoiceForReportPeriod = InvoiceItem::where('product_variant_id', $variant->product_variant_id)
                            ->sum(DB::raw('quantity + IFNULL(free,0)')) ?? 0;

                        $soldPOSForReportPeriod = SaleItem::where('product_variant_id', $variant->product_variant_id)
                            ->sum(DB::raw('quantity + IFNULL(free_qty,0)')) ?? 0;

                        $soldForReportPeriod = $soldInvoiceForReportPeriod + $soldPOSForReportPeriod;

                        // Use stored closing stock value
                        $closingStock = $variant->closing_stock_quantity ?? 0;
                        $openingStock = $variant->opening_stock_quantity ?? 0;

                        return [
                            'batchNumber' => $variant->batch_number ?? 'N/A',
                            'expiryDate' => $variant->expiry_date ?? 'N/A',
                            'openingStock' => $openingStock,
                            'purchased' => $purchasedForReportPeriod,
                            'sold' => $soldForReportPeriod,
                            'closingStock' => $closingStock,
                            'buyingCost' => $variant->buying_cost ?? $variant->product->buying_cost ?? 0,
                            'salesPrice' => $variant->sales_price ?? $variant->product->sales_price ?? 0,
                            'mrp' => $variant->mrp ?? 0,
                            'storeLocation' => $variant->store_location ?? 'N/A',
                            'cabinet' => $variant->cabinet ?? 'N/A',
                            'row' => $variant->row ?? 'N/A',
                            'barcode' => $variant->barcode ?? 'N/A',
                            'status' => $closingStock > 0 ? 'In Stock' : 'Out of Stock',
                            'turnoverRate' => 0, // Optional: set to 0 or calculate if needed
                            'adjustment' => 0, // Optional: set to 0 or calculate if needed
                        ];
                    });

                    $batchData = $batchData->filter();

                    $totalClosingStock = $batchData->sum('closingStock');
                    $totalPurchased = $batchData->sum('purchased');
                    $totalSold = $batchData->sum('sold');
                    $totalOpeningStock = $batchData->sum('openingStock');

                    $totalOpeningValue = $batchData->sum(function($b) { return ($b['openingStock'] ?? 0) * ($b['buyingCost'] ?? 0); });
                    $totalPurchaseValue = $batchData->sum(function($b) { return ($b['purchased'] ?? 0) * ($b['buyingCost'] ?? 0); });
                    $totalSalesValue = $batchData->sum(function($b) { return ($b['sold'] ?? 0) * ($b['salesPrice'] ?? 0); });
                    $totalClosingValue = $batchData->sum(function($b) { return ($b['closingStock'] ?? 0) * ($b['buyingCost'] ?? 0); });

                    return [
                        'itemCode' => $product->item_code ?? 'N/A',
                        'itemName' => $product->product_name ?? 'N/A',
                        'category' => $product->category ?? 'N/A',
                        'supplier' => $product->supplier ?? 'N/A',
                        'unit' => $product->unit_type ?? 'N/A',
                        'initialOpeningStock' => $totalOpeningStock,
                        'openingStock' => $totalOpeningStock,
                        'purchased' => $totalPurchased,
                        'sold' => $totalSold,
                        'adjusted' => 0,
                        'closingStock' => $totalClosingStock,
                        'costPrice' => $product->buying_cost ?? 0,
                        'sellingPrice' => $product->sales_price ?? 0,
                        'totalOpeningValue' => $totalOpeningValue,
                        'totalPurchaseValue' => $totalPurchaseValue,
                        'totalSalesValue' => $totalSalesValue,
                        'totalClosingValue' => $totalClosingValue,
                        'location' => [
                            'type' => explode(' ', $product->store_location ?? 'Unknown N/A', 2)[0] ?? 'Unknown',
                            'identifier' => explode(' ', $product->store_location ?? 'Unknown N/A', 2)[1] ?? 'N/A',
                        ],
                        'hasBatches' => true,
                        'batches' => $batchData->toArray(),
                    ];
                }

                // Non-batched product logic
                $openingStock = $product->opening_stock_quantity ?? 0;

                if ($fromDate && $firstPurchaseDate && $fromDate !== $firstPurchaseDate) {
                    $previousDay = date('Y-m-d', strtotime($fromDate . ' -1 day'));

                    $totalInvoiceSoldPrev = InvoiceItem::where('product_id', $product->product_id)
                        ->whereHas('invoice', function ($q) use ($previousDay) {
                            $q->where('created_at', '<=', $previousDay);
                        })
                        ->sum(DB::raw('quantity + IFNULL(free,0)')) ?? 0;

                    $totalPosSoldPrev = SaleItem::where('product_id', $product->product_id)
                        ->whereHas('sale', function ($q) use ($previousDay) {
                            $q->where('created_at', '<=', $previousDay . ' 23:59:59');
                        })
                        ->sum(DB::raw('quantity + IFNULL(free_qty,0)')) ?? 0;

                    $totalPurchasedPrev = PurchaseItem::where('product_id', $product->product_id)
                        ->whereHas('purchase', function ($q) use ($previousDay) {
                            $q->where('created_at', '<=', $previousDay);
                        })
                        ->sum('quantity') ?? 0;

                    $totalPurchaseReturnPrev = \App\Models\PurchaseReturnItem::where('product_id', $product->product_id)
                        ->whereHas('purchaseReturn', function ($q) use ($previousDay) {
                            $q->where('created_at', '<=', $previousDay)->where('status', 'approved');
                        })
                        ->sum('quantity') ?? 0;

                    $openingStock = ($product->opening_stock_quantity ?? 0) + $totalPurchasedPrev - $totalInvoiceSoldPrev - $totalPosSoldPrev - $totalPurchaseReturnPrev;
                }

                // Use stored closing stock value for products without variants
                $closingStock = $product->closing_stock_quantity ?? 0;
                
                // Get transaction history for display purposes (always calculate)
                $totalPurchased = PurchaseItem::where('product_id', $product->product_id)
                    ->sum('quantity') ?? 0;

                $totalSold = (InvoiceItem::where('product_id', $product->product_id)
                    ->sum(DB::raw('quantity + IFNULL(free,0)')) ?? 0) +
                    (SaleItem::where('product_id', $product->product_id)
                    ->sum(DB::raw('quantity + IFNULL(free_qty,0)')) ?? 0);

                $totalSalesReturn = \App\Models\SalesReturnItem::where('product_id', $product->product_id)
                    ->whereHas('salesReturn', function ($q) {
                        $q->where('status', 'approved');
                    })
                    ->sum('quantity') ?? 0;

                $totalPurchaseReturn = \App\Models\PurchaseReturnItem::where('product_id', $product->product_id)
                    ->whereHas('purchaseReturn', function ($q) {
                        $q->where('status', 'approved');
                    })
                    ->sum('quantity') ?? 0;
                
                $adjustment = 0; // No adjustment needed when using stored values
                $itemCode = $product->item_code ?? 'N/A';

                return [
                    'itemCode' => $itemCode,
                    'itemName' => $product->product_name ?? 'N/A',
                    'category' => $product->category ?? 'N/A',
                    'supplier' => $product->supplier ?? 'N/A',
                    'unit' => $product->unit_type ?? 'N/A',
                    'initialOpeningStock' => $product->opening_stock_quantity ?? 0,
                    'openingStock' => $openingStock,
                    'purchased' => $totalPurchased,
                    'sold' => $totalSold,
                    'adjusted' => $adjustment,
                    'closingStock' => $closingStock,
                    'costPrice' => $product->buying_cost ?? 0,
                    'sellingPrice' => $product->sales_price ?? 0,
                    'totalPurchaseValue' => $closingStock * ($product->buying_cost ?? 0),
                    'totalSalesValue' => $closingStock * ($product->sales_price ?? 0),
                    'location' => [
                        'type' => explode(' ', $product->store_location ?? 'Unknown N/A', 2)[0] ?? 'Unknown',
                        'identifier' => explode(' ', $product->store_location ?? 'Unknown N/A', 2)[1] ?? 'N/A',
                    ],
                    'hasBatches' => false,
                    'batches' => [],
                ];
            });

            Log::info('Detailed stock reports prepared:', $stockReports->toArray());

            return response()->json($stockReports, 200);

        } catch (\Exception $e) {
            Log::error('Error fetching detailed stock report: ' . $e->getMessage() . "\nStack Trace: " . $e->getTraceAsString());
            return response()->json(['error' => 'Failed to fetch detailed stock report: ' . $e->getMessage()], 500);
        }
    }

    private function calculateVariantClosingStock(ProductVariant $variant, $fromDate = null, $toDate = null)
    {
        $latestRecheck = \App\Models\StockRecheck::where('product_variant_id', $variant->product_variant_id)
            ->where('recheck_type', 'batch')
            ->where('update_actual_stock', true)
            ->orderBy('created_at', 'desc')
            ->first();

        if ($latestRecheck && $latestRecheck->corrected_closing_stock !== null) {
            $recheckDate = $latestRecheck->created_at;
            $baseClosingStock = $latestRecheck->corrected_closing_stock;

            $purchasedAfterRecheck = PurchaseItem::where('product_variant_id', $variant->product_variant_id)
                ->whereHas('purchase', function ($q) use ($recheckDate, $toDate) {
                    $q->where('created_at', '>=', $recheckDate);
                    if ($toDate) {
                        $q->where('created_at', '<=', $toDate . ' 23:59:59');
                    }
                })
                ->sum('quantity') ?? 0;

            $soldAfterRecheckInvoice = InvoiceItem::where('product_variant_id', $variant->product_variant_id)
                ->whereHas('invoice', function ($q) use ($recheckDate, $toDate) {
                    $q->where('created_at', '>=', $recheckDate);
                    if ($toDate) {
                        $q->where('created_at', '<=', $toDate . ' 23:59:59');
                    }
                })
                ->sum(DB::raw('quantity + IFNULL(free,0)')) ?? 0;

            $soldAfterRecheckPOS = SaleItem::where('product_variant_id', $variant->product_variant_id)
                ->whereHas('sale', function ($q) use ($recheckDate, $toDate) {
                    $q->where('created_at', '>=', $recheckDate);
                    if ($toDate) {
                        $q->where('created_at', '<=', $toDate . ' 23:59:59');
                    }
                })
                ->sum(DB::raw('quantity + IFNULL(free_qty,0)')) ?? 0;

            $soldAfterRecheck = $soldAfterRecheckInvoice + $soldAfterRecheckPOS;

            $closingStock = $baseClosingStock + $purchasedAfterRecheck - $soldAfterRecheck;
            $purchased = $purchasedAfterRecheck;
            $sold = $soldAfterRecheck;
        } else {
            // Fallback to original calculation if no recheck
            $purchased = PurchaseItem::where('product_variant_id', $variant->product_variant_id)
                ->whereHas('purchase', function ($q) use ($fromDate, $toDate) {
                    if ($fromDate && $toDate) {
                        $q->whereBetween('date_of_purchase', [$fromDate, $toDate]);
                    }
                })
                ->sum('quantity') ?? 0;

            $soldInvoice = InvoiceItem::where('product_variant_id', $variant->product_variant_id)
                ->whereHas('invoice', function ($q) use ($fromDate, $toDate) {
                    if ($fromDate && $toDate) {
                        $q->whereBetween('invoice_date', [$fromDate, $toDate]);
                    }
                })
                ->sum(DB::raw('quantity + IFNULL(free,0)')) ?? 0;

            $soldPOS = SaleItem::where('product_variant_id', $variant->product_variant_id)
                ->whereHas('sale', function ($q) use ($fromDate, $toDate) {
                    if ($fromDate && $toDate) {
                        $q->whereBetween('created_at', [$fromDate . ' 00:00:00', $toDate . ' 23:59:59']);
                    }
                })
                ->sum(DB::raw('quantity + IFNULL(free_qty,0)')) ?? 0;

            $sold = $soldInvoice + $soldPOS;
            $closingStock = $variant->opening_stock_quantity + $purchased - $sold;
        }

        return max(0, $closingStock);
    }

    public function updateBatchClosingStock()
    {
        Log::info('Starting batch-wise closing stock calculation');

        try {
            $closingStockService = new ClosingStockService();
            $result = $closingStockService->updateAllClosingStock();
            
            Log::info("Batch closing stock calculation completed. Updated {$result['variants_updated']} variants and {$result['products_updated']} products.");
            return response()->json([
                'message' => 'Batch closing stock updated successfully',
                'variants_updated' => $result['variants_updated'],
                'products_updated' => $result['products_updated']
            ], 200);

        } catch (\Exception $e) {
            Log::error('Error updating batch closing stock: ' . $e->getMessage() . "\nStack Trace: " . $e->getTraceAsString());
            return response()->json(['error' => 'Failed to update batch closing stock: ' . $e->getMessage()], 500);
        }
    }

    public function batchReport(Request $request)
    {
        Log::info('Fetching batch-wise stock report with filters:', $request->all());
        try {
            $query = ProductVariant::with('product');

            if ($request->has('itemCode') && $request->itemCode !== '') {
                $query->whereHas('product', function ($q) use ($request) {
                    $q->where('item_code', 'like', '%' . $request->itemCode . '%');
                });
            }
            if ($request->has('batchNumber') && $request->batchNumber !== '') {
                $query->where('batch_number', 'like', '%' . $request->batchNumber . '%');
            }
            if ($request->has('location') && $request->location !== '') {
                $query->where('store_location', 'like', '%' . $request->location . '%');
            }

            $variants = $query->get();
            Log::info('Product variants fetched:', $variants->toArray());

            $batchReports = $variants->map(function ($variant) {
                $product = $variant->product;
                return [
                    'itemCode' => $product->item_code ?? 'N/A',
                    'itemName' => $product->product_name ?? 'N/A',
                    'batchNumber' => $variant->batch_number ?? 'N/A',
                    'expiryDate' => $variant->expiry_date ?? 'N/A',
                    'location' => $variant->store_location ?? 'N/A',
                    'closingStock' => $variant->closing_stock_quantity ?? 0,
                ];
            });

            Log::info('Batch stock reports prepared:', $batchReports->toArray());

            if ($product->item_code === 'milk') {
                \Log::info('Milk batches in stock report:', $variants->map(function ($variant) {
                    return [
                        'batchNumber' => $variant->batch_number,
                        'closingStock' => $variant->closing_stock_quantity,
                        'expiryDate' => $variant->expiry_date,
                        'location' => $variant->store_location,
                    ];
                })->toArray());
            }

            return response()->json($batchReports, 200);
        } catch (\Exception $e) {
            Log::error('Error fetching batch stock report: ' . $e->getMessage() . "\nStack Trace: " . $e->getTraceAsString());
            return response()->json(['error' => 'Failed to fetch batch stock report: ' . $e->getMessage()], 500);
        }
    }
}