import React, { useState, useEffect, useRef } from "react";
import { toast } from "react-toastify";
import {
  FiSearch,
  FiHash,
  FiPackage,
  FiCheckCircle,
  FiTrash2,
  FiArchive,
  FiUser,
  FiEye,
  FiSave,
  FiAlertCircle,
  FiEdit2,
  FiChevronDown,
  FiInfo,
  FiChevronUp,
  FiCheck,
} from "react-icons/fi";
import { useAuth } from "../../context/NewAuthContext";
import { getApi } from "../../services/api";

const SalesReturn = () => {
  const { user } = useAuth();
  const api = getApi();
  const [returnItems, setReturnItems] = useState([]);
  const [products, setProducts] = useState([]);
  const [invoices, setInvoices] = useState([]);
  const [filteredInvoices, setFilteredInvoices] = useState([]);
  const [newReturn, setNewReturn] = useState({
    salesReturnNumber: "",
    invoiceOrBillNumber: "",
    customerName: "",
    type: "",
    items: [],
    refundMethod: "cash",
    remarks: "",
    status: "pending",
  });
  const [itemForm, setItemForm] = useState({
    product_id: "",
    variant_id: "",
    quantity: "",
    selling_cost: "",
    reason: "",
    batch_number: "",
    expiry_date: "",
    max_quantity: null,
  });
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [selectedBatch, setSelectedBatch] = useState(null);
  const [showProductDropdown, setShowProductDropdown] = useState(false);
  const [productHighlightedIndex, setProductHighlightedIndex] = useState(-1);
  const [productSearchQuery, setProductSearchQuery] = useState("");
  const [invoiceSearch, setInvoiceSearch] = useState("");
  const [editMode, setEditMode] = useState(false);
  const [editReturnId, setEditReturnId] = useState(null);
  const [viewReturn, setViewReturn] = useState(null);
  const [expandedRows, setExpandedRows] = useState([]);
  const [showInvoiceSuggestions, setShowInvoiceSuggestions] = useState(false);
  const [invoiceHighlightedIndex, setInvoiceHighlightedIndex] = useState(-1);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [quantityError, setQuantityError] = useState("");

  // New state for bill items
  const [billItems, setBillItems] = useState([]);
  const [selectedBillId, setSelectedBillId] = useState(null);
  const [selectedBillType, setSelectedBillType] = useState(null); // 'sale' or 'invoice'

  const invoiceSearchRef = useRef(null);
  const productSelectRef = useRef(null);
  const invoiceSearchInputRef = useRef(null);
  const quantityInputRef = useRef(null);
  const sellingCostInputRef = useRef(null);
  const reasonInputRef = useRef(null);

  // Set initial sales return number when not in edit mode
  useEffect(() => {
    if (!editMode) {
      // Fetch next sales return number from backend
      const fetchNextSalesReturnNumber = async () => {
        try {
          const response = await api.get("/api/sales-returns/next-number");
          const nextNumber = response.data.next_sales_return_number;
          setNewReturn((prev) => ({
            ...prev,
            salesReturnNumber: nextNumber,
          }));
        } catch (error) {
          toast.error("Failed to fetch next sales return number");
          setNewReturn((prev) => ({
            ...prev,
            salesReturnNumber: "",
          }));
        }
      };
      fetchNextSalesReturnNumber();
    }
  }, [editMode]);

  // Fetch invoices, sales, products, and sales returns on mount
  useEffect(() => {
    if (user?.token) {
      fetchData();
    } else {
      toast.error("Please login to access this form");
      setErrors({ auth: "User not authenticated" });
    }
  }, [user]);

  const fetchData = async () => {
    setLoading(true);
    try {
      const timestamp = new Date().getTime();
      const [invoicesRes, salesRes, productsRes, returnsRes] =
        await Promise.all([
          api.get(`/api/invoices?_t=${timestamp}`, {
            headers: { Accept: "application/json" },
          }),
          api.get(`/api/sales?_t=${timestamp}`, {
            headers: { Accept: "application/json" },
          }),
          api.get(`/api/products?_t=${timestamp}`),
          api.get(`/api/sales-returns?_t=${timestamp}`),
        ]);

      // Process invoices
      let invoicesData = [];
      if (Array.isArray(invoicesRes.data.data)) {
        invoicesData = invoicesRes.data.data;
      } else if (Array.isArray(invoicesRes.data)) {
        invoicesData = invoicesRes.data;
      } else {
        throw new Error("Invalid invoices data format");
      }

      // Process sales
      let salesData = [];
      if (Array.isArray(salesRes.data.data)) {
        salesData = salesRes.data.data;
      } else if (Array.isArray(salesRes.data)) {
        salesData = salesRes.data;
      } else {
        throw new Error("Invalid sales data format");
      }

      // Combine and normalize invoice and bill numbers
      const normalizedInvoices = invoicesData.map((inv) => ({
        id: inv.id,
        invoiceNumber: inv.invoice_no || `INV-${inv.id}`,
        customerName: inv.customer_name || "Walk-in Customer",
        type: "invoice",
      }));

      const normalizedSales = salesData.map((sale) => ({
        id: sale.id,
        invoiceNumber: sale.bill_number || `SALE-${sale.id}`,
        customerName: sale.customer_name || "Walk-in Customer",
        type: "sale",
      }));

      // Combine invoices and sales, remove duplicates, and sort
      const combinedInvoices = [...normalizedInvoices, ...normalizedSales]
        .filter(
          (item, index, self) =>
            index ===
            self.findIndex((t) => t.invoiceNumber === item.invoiceNumber)
        )
        .sort((a, b) => a.invoiceNumber.localeCompare(b.invoiceNumber));

      // Process products with variants
      let productsData = [];
      if (Array.isArray(productsRes.data.data)) {
        productsData = productsRes.data.data;
      } else if (Array.isArray(productsRes.data)) {
        productsData = productsRes.data;
      } else {
        throw new Error("Invalid products data format");
      }

      // Debug: Log raw products data
      console.log("Raw products data in SalesReturn:", productsData);

      // Generate filtered product options with variants (similar to POS form)
      const productOptions = [];
      productsData.forEach((product) => {
        console.log(
          `Processing product: ${product.product_name}`,
          product.variants
        );

        if (product.variants && product.variants.length > 0) {
          // Product has variants - create option for each variant
          product.variants.forEach((variant) => {
            const batchInfo = variant.batch_number
              ? ` (Batch: ${variant.batch_number})`
              : "";
            const expiryInfo = variant.expiry_date
              ? ` (Exp: ${variant.expiry_date.split("T")[0]})`
              : "";

            const option = {
              ...product,
              ...variant,
              // Use variant-specific values
              product_id: product.product_id,
              variant_id:
                variant.product_variant_id ||
                variant.variant_id ||
                `${product.product_id}-${variant.batch_number}`,
              display_name: `${product.product_name}${batchInfo}${expiryInfo}`,
              stock: parseFloat(variant.opening_stock_quantity || 0),
              sales_price: parseFloat(variant.sales_price || 0),
              mrp: parseFloat(variant.mrp || 0),
              buying_cost: parseFloat(variant.buying_cost || 0),
              wholesale_price: parseFloat(variant.wholesale_price || 0),
              minimum_price: parseFloat(variant.minimum_price || 0),
              batch_number: variant.batch_number,
              expiry_date: variant.expiry_date,
              barcode: variant.barcode || "N/A",
              cabinet: variant.cabinet,
              row: variant.row,
              minimum_stock_quantity: variant.minimum_stock_quantity || 0,
            };

            productOptions.push(option);
          });
        } else {
          // No variants - add product as is
          const option = {
            ...product,
            variant_id: null,
            display_name: product.product_name,
            stock: parseFloat(product.opening_stock_quantity || 0),
            sales_price: parseFloat(product.sales_price || 0),
            mrp: parseFloat(product.mrp || 0),
            buying_cost: parseFloat(product.buying_cost || 0),
            batch_number: null,
            expiry_date: null,
            barcode: "N/A",
          };

          productOptions.push(option);
        }
      });

      // Debug: Log final product options
      console.log("Final product options in SalesReturn:", productOptions);

      // Process sales returns
      let returnsData = [];
      if (Array.isArray(returnsRes.data.data)) {
        returnsData = returnsRes.data.data;
      } else if (Array.isArray(returnsRes.data)) {
        returnsData = returnsRes.data;
      } else {
        throw new Error("Invalid sales returns data format");
      }

      returnsData.forEach((returnItem) => {
        if (returnItem.items) {
          returnItem.items.forEach((item) => {
            item.selling_cost = parseFloat(item.selling_cost) || "";
          });
        }
      });

      // Sort returns by sales_return_number
      returnsData.sort((a, b) =>
        a.sales_return_number.localeCompare(b.sales_return_number)
      );

      setInvoices(combinedInvoices);
      setProducts(productOptions);
      setReturnItems(returnsData);

      if (productsData.length === 0) {
        setErrors((prev) => ({
          ...prev,
          products: "No products available. Please add products in the system.",
        }));
        toast.warn("No products available");
      }
      if (combinedInvoices.length === 0) {
        setErrors((prev) => ({
          ...prev,
          invoices: "No invoices or bills available.",
        }));
        toast.warn("No invoices or bills available");
      }
      if (returnsData.length === 0) {
        setErrors((prev) => ({
          ...prev,
          returns: "No sales returns found.",
        }));
      }
    } catch (error) {
      const errorMsg = error.response?.data?.message || "Error fetching data";
      setErrors({ fetch: errorMsg });
      toast.error(errorMsg);
      if (error.response?.status === 401) {
        toast.error("Session expired. Please login again.");
      }
    } finally {
      setLoading(false);
    }
  };

  // Auto-fetch customer name and type based on invoice/bill number
  useEffect(() => {
    if (newReturn.invoiceOrBillNumber) {
      const invoice = invoices.find(
        (inv) => inv.invoiceNumber === newReturn.invoiceOrBillNumber
      );
      if (invoice) {
        setNewReturn((prev) => ({
          ...prev,
          customerName: invoice.customerName,
          type: invoice.type,
        }));
      } else {
        setNewReturn((prev) => ({
          ...prev,
          customerName: "",
          type: "",
        }));
      }
    } else {
      setNewReturn((prev) => ({
        ...prev,
        customerName: "",
        type: "",
      }));
    }
  }, [newReturn.invoiceOrBillNumber, invoices]);

  // Filter invoices based on search query
  useEffect(() => {
    if (invoiceSearch.trim()) {
      const query = invoiceSearch.toLowerCase();
      const filtered = invoices.filter((inv) =>
        inv.invoiceNumber.toLowerCase().includes(query)
      );
      setFilteredInvoices(filtered);
      setShowInvoiceSuggestions(true);
      setInvoiceHighlightedIndex(-1);
    } else {
      setFilteredInvoices([]);
      setShowInvoiceSuggestions(false);
      setInvoiceHighlightedIndex(-1);
    }
  }, [invoiceSearch, invoices]);

  // Handle clicks outside to close suggestions
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        invoiceSearchRef.current &&
        !invoiceSearchRef.current.contains(event.target)
      ) {
        setShowInvoiceSuggestions(false);
        setInvoiceHighlightedIndex(-1);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewReturn((prev) => ({ ...prev, [name]: value }));
  };

  // Function to validate quantity
  const validateQuantity = (quantity) => {
    if (!quantity || quantity <= 0) {
      setQuantityError("Quantity must be greater than 0");
      return false;
    }

    if (itemForm.max_quantity && parseFloat(quantity) > itemForm.max_quantity) {
      setQuantityError(
        `Cannot exceed original sold quantity (${itemForm.max_quantity})`
      );
      return false;
    }

    setQuantityError("");
    return true;
  };

  const handleItemFormChange = (e) => {
    const { name, value } = e.target;

    // Real-time validation for quantity field
    if (name === "quantity") {
      validateQuantity(value);
    }

    setItemForm((prev) => ({
      ...prev,
      [name]:
        name === "quantity"
          ? parseFloat(value) >= 0
            ? parseFloat(value)
            : 0
          : name === "selling_cost"
            ? parseFloat(value) || ""
            : value,
    }));
  };

  const handleSelectProductBatch = (productBatch) => {
    setSelectedProduct(productBatch);
    setSelectedBatch(productBatch);
    setProductSearchQuery(productBatch.display_name);
    setShowProductDropdown(false);
    setProductHighlightedIndex(-1);

    // Clear quantity error when selecting new product
    setQuantityError("");

    // Set item form with batch information if available
    setItemForm({
      ...itemForm,
      product_id: productBatch.product_id.toString(),
      variant_id: productBatch.product_variant_id || productBatch.variant_id,
      batch_number: productBatch.batch_number || "",
      expiry_date: productBatch.expiry_date || "",
      selling_cost: parseFloat(
        productBatch.sales_price || productBatch.unit_price || 0
      ),
      // Set max quantity if this is from a bill
      max_quantity: productBatch.max_return_quantity || null,
    });

    quantityInputRef.current?.focus();
  };

  // Filter products based on search query
  // If a bill is selected, only show items from that bill, otherwise show all products
  const availableProducts = billItems.length > 0 ? billItems : products;
  const startsWithOnly = localStorage.getItem("productSearchStartsWithOnly") === "true";
  const filteredProducts = availableProducts.filter((product) => {
    const label = product.display_name.toLowerCase();
    const search = productSearchQuery.toLowerCase();
    if (startsWithOnly) {
      return label.startsWith(search);
    } else {
      return label.includes(search);
    }
  });

  // Handle product search input change
  const handleProductSearchChange = (e) => {
    const value = e.target.value;
    setProductSearchQuery(value);
    setShowProductDropdown(value.length > 0);
    setProductHighlightedIndex(-1);
    if (value === "") {
      setSelectedProduct(null);
      setSelectedBatch(null);
      setItemForm({
        ...itemForm,
        product_id: "",
        variant_id: "",
        selling_cost: 0,
      });
    }
  };

  // Handle keyboard navigation for product dropdown
  const handleProductKeyDown = (e) => {
    if (!showProductDropdown) {
      if (e.key === "ArrowDown" || e.key === "Enter") {
        setShowProductDropdown(true);
        setProductHighlightedIndex(0);
        e.preventDefault();
      }
      return;
    }

    switch (e.key) {
      case "ArrowDown":
        e.preventDefault();
        setProductHighlightedIndex((prev) =>
          prev < filteredProducts.length - 1 ? prev + 1 : prev
        );
        break;
      case "ArrowUp":
        e.preventDefault();
        setProductHighlightedIndex((prev) => (prev > 0 ? prev - 1 : prev));
        break;
      case "Enter":
        e.preventDefault();
        if (
          productHighlightedIndex >= 0 &&
          productHighlightedIndex < filteredProducts.length
        ) {
          handleSelectProductBatch(filteredProducts[productHighlightedIndex]);
        }
        break;
      case "Escape":
        e.preventDefault();
        setShowProductDropdown(false);
        setProductHighlightedIndex(-1);
        break;
    }
  };

  // Function to fetch items from selected bill/invoice
  const fetchBillItems = async (billNumber, billType) => {
    try {
      setLoading(true);
      let response;
      let billId;

      if (billType === "invoice") {
        // Find invoice by invoice number
        const invoice = invoices.find(
          (inv) => inv.invoiceNumber === billNumber
        );
        if (!invoice) {
          toast.error("Invoice not found");
          return;
        }
        billId = invoice.id;
        response = await api.get(`/api/invoices/${billId}`);
      } else {
        // Find sale by bill number
        const sale = invoices.find(
          (inv) => inv.invoiceNumber === billNumber && inv.type === "sale"
        );
        if (!sale) {
          toast.error("Sale not found");
          return;
        }
        billId = sale.id;
        response = await api.get(`/api/sales/${billId}`);
      }

      const billData = response.data;
      const items = billData.items || [];

      console.log(`${billType} data:`, billData);
      console.log(`${billType} items:`, items);

      // Transform items to include batch information and make them selectable
      const transformedItems = items.map((item) => ({
        id: item.id,
        product_id: item.product_id,
        product_variant_id: item.product_variant_id || null,
        batch_number: item.batch_number || null,
        expiry_date: item.expiry_date || null,
        product_name: item.product_name || item.description,
        quantity_sold: item.quantity, // Original quantity sold
        max_return_quantity: item.quantity, // Maximum that can be returned
        unit_price: parseFloat(item.unit_price || item.sales_price || 0),
        sales_price: parseFloat(item.sales_price || item.unit_price || 0),
        display_name: `${item.product_name || item.description}${item.batch_number ? ` - Batch: ${item.batch_number}` : ""}`,
      }));

      setBillItems(transformedItems);
      setSelectedBillId(billId);
      setSelectedBillType(billType);

      toast.success(`Loaded ${transformedItems.length} items from ${billType}`);
    } catch (error) {
      console.error("Error fetching bill items:", error);
      toast.error("Failed to load items from bill");
      setBillItems([]);
      setSelectedBillId(null);
      setSelectedBillType(null);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectInvoice = (invoice) => {
    setNewReturn((prev) => ({
      ...prev,
      invoiceOrBillNumber: invoice.invoiceNumber,
      customerName: invoice.customerName,
      type: invoice.type,
    }));
    setInvoiceSearch(invoice.invoiceNumber);
    setShowInvoiceSuggestions(false);
    setInvoiceHighlightedIndex(-1);

    // Fetch items from the selected bill
    fetchBillItems(invoice.invoiceNumber, invoice.type);

    productSelectRef.current?.focus();
  };

  const handleInvoiceSearchKeyDown = (e) => {
    if (e.key === "ArrowDown") {
      e.preventDefault();
      setInvoiceHighlightedIndex((prev) =>
        prev < filteredInvoices.length - 1 ? prev + 1 : prev
      );
    } else if (e.key === "ArrowUp") {
      e.preventDefault();
      setInvoiceHighlightedIndex((prev) => (prev > 0 ? prev - 1 : prev));
    } else if (e.key === "Enter" && invoiceHighlightedIndex >= 0) {
      e.preventDefault();
      handleSelectInvoice(filteredInvoices[invoiceHighlightedIndex]);
    } else if (
      e.key === "Enter" &&
      !showInvoiceSuggestions &&
      newReturn.invoiceOrBillNumber
    ) {
      e.preventDefault();
      productSelectRef.current?.focus();
    }
  };

  const handleQuantityKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      sellingCostInputRef.current?.focus();
      sellingCostInputRef.current?.select();
    }
  };

  const handleSellingCostKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      reasonInputRef.current?.focus();
      reasonInputRef.current?.select();
    }
  };

  const handleReasonKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      // Focus the add button
      const addButton = document.querySelector("[data-add-item-button]");
      if (addButton) {
        addButton.focus();
      }
    }
  };

  const handleAddButtonKeyDown = (e) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      if (selectedProduct && selectedBatch && itemForm.quantity > 0) {
        handleAddItem();
      }
    }
  };

  const handleAddItem = () => {
    if (!selectedProduct) {
      toast.error("Please select a valid product");
      return;
    }
    if (!selectedBatch) {
      toast.error("Please select a batch");
      return;
    }
    if (itemForm.quantity <= 0) {
      toast.error("Quantity must be greater than 0");
      return;
    }
    if (itemForm.selling_cost < 0) {
      toast.error("Selling cost cannot be negative");
      return;
    }

    // Validate quantity using the validation function
    if (!validateQuantity(itemForm.quantity)) {
      // Error message is already set by validateQuantity function
      return;
    }

    // Check if the same product+variant combination already exists
    const existingItemIndex = newReturn.items.findIndex(
      (item) =>
        item.product_id === itemForm.product_id &&
        item.variant_id === itemForm.variant_id
    );

    if (existingItemIndex !== -1) {
      // Update existing item quantity instead of creating new line
      const updatedItems = [...newReturn.items];
      const existingItem = updatedItems[existingItemIndex];
      const newQuantity =
        parseFloat(existingItem.quantity) + parseFloat(itemForm.quantity);

      updatedItems[existingItemIndex] = {
        ...existingItem,
        quantity: newQuantity,
        // Keep the same selling cost and reason from existing item
        // If user wants different price/reason, they should edit the existing line
      };

      setNewReturn({
        ...newReturn,
        items: updatedItems,
      });

      toast.success(
        `Updated quantity for ${selectedProduct.product_name} (Batch: ${selectedBatch.batch_number})`
      );
    } else {
      // Add new item as usual
      const newItem = {
        product_id: itemForm.product_id,
        variant_id: itemForm.variant_id,
        product_name: selectedProduct.product_name,
        batch_number: selectedBatch.batch_number,
        barcode: selectedBatch.barcode,
        expiry_date: selectedBatch.expiry_date,
        quantity: itemForm.quantity,
        selling_cost: itemForm.selling_cost,
        reason: itemForm.reason || null,
      };

      setNewReturn({
        ...newReturn,
        items: [...newReturn.items, newItem],
      });

      toast.success(
        `Added ${selectedProduct.product_name} (Batch: ${selectedBatch.batch_number})`
      );
    }

    // Reset form
    setItemForm({
      product_id: "",
      variant_id: "",
      quantity: "",
      selling_cost: 0,
      reason: "",
      batch_number: "",
      expiry_date: "",
      max_quantity: null,
    });
    setSelectedProduct(null);
    setSelectedBatch(null);
    setProductSearchQuery("");
    setShowProductDropdown(false);
    setProductHighlightedIndex(-1);
    setQuantityError(""); // Clear quantity error
    productSelectRef.current?.focus();
  };

  const handleRemoveItem = (index) => {
    setNewReturn({
      ...newReturn,
      items: newReturn.items.filter((_, i) => i !== index),
    });
  };

  const handleEditReturn = async (returnItem) => {
    try {
      const response = await api.get(`/api/sales-returns/${returnItem.id}`);
      const data = response.data.data;
      setNewReturn({
        salesReturnNumber: data.sales_return_number,
        invoiceOrBillNumber: data.invoice_no || data.bill_number,
        customerName: data.customer_name,
        type: data.invoice_no ? "invoice" : "sale",
        items: data.items.map((item) => ({
          product_id: item.product_id.toString(),
          product_name: item.product_name,
          quantity: item.quantity,
          selling_cost: parseFloat(item.selling_cost),
          reason: item.reason || "",
        })),
        refundMethod: data.refund_method,
        remarks: data.remarks || "",
        status: data.status,
      });
      setInvoiceSearch(data.invoice_no || data.bill_number);
      setEditMode(true);
      setEditReturnId(returnItem.id);
      window.scrollTo({ top: 0, behavior: "smooth" });
    } catch (error) {
      const errorMsg = error.response?.data?.message || "Error fetching return";
      toast.error(errorMsg);
    }
  };

  const handleViewReturn = async (returnItem) => {
    try {
      const response = await api.get(`/api/sales-returns/${returnItem.id}`);
      setViewReturn(response.data.data);
    } catch (error) {
      const errorMsg = error.response?.data?.message || "Error fetching return";
      toast.error(errorMsg);
    }
  };

  const handleCloseView = () => {
    setViewReturn(null);
  };

  const handleCancelEdit = () => {
    setEditMode(false);
    setEditReturnId(null);
    setNewReturn({
      salesReturnNumber: "",
      invoiceOrBillNumber: "",
      customerName: "",
      type: "",
      items: [],
      refundMethod: "cash",
      remarks: "",
      status: "pending",
    });
    setItemForm({
      product_id: "",
      variant_id: "",
      quantity: "",
      selling_cost: 0,
      reason: "",
    });
    setSelectedProduct(null);
    setSelectedBatch(null);
    setInvoiceSearch("");
  };

  const handleSubmitReturn = async () => {
    if (
      !newReturn.salesReturnNumber ||
      !newReturn.invoiceOrBillNumber ||
      !newReturn.customerName ||
      newReturn.items.length === 0
    ) {
      toast.error("Please fill all required fields");
      return;
    }

    setLoading(true);
    try {
      const returnData = {
        sales_return_number: newReturn.salesReturnNumber,
        customer_name: newReturn.customerName,
        items: newReturn.items.map((item) => ({
          ...item,
          selling_cost: item.selling_cost,
        })),
        refund_method: newReturn.refundMethod,
        remarks: newReturn.remarks,
        status: newReturn.status,
      };

      if (newReturn.type === "invoice") {
        returnData.invoice_no = newReturn.invoiceOrBillNumber;
        returnData.bill_number = null;
      } else if (newReturn.type === "sale") {
        returnData.bill_number = newReturn.invoiceOrBillNumber;
        returnData.invoice_no = null;
      } else {
        throw new Error("Invalid type: must be 'invoice' or 'sale'");
      }

      let response;
      if (editMode) {
        response = await api.put(
          `/api/sales-returns/${editReturnId}`,
          returnData
        );
        toast.success("Sales return updated successfully!");
        const updatedItems = returnItems
          .map((item) => (item.id === editReturnId ? response.data.data : item))
          .sort((a, b) =>
            a.sales_return_number.localeCompare(b.sales_return_number)
          );
        setReturnItems(updatedItems);
        handleCancelEdit();
        window.location.reload();
      } else {
        response = await api.post("/api/sales-returns", returnData);
        toast.success("Sales return submitted successfully!");
        const newReturnItem = {
          ...response.data.data,
          items: response.data.data.items.map((item) => ({
            ...item,
            selling_cost: parseFloat(item.selling_cost) || 0,
          })),
        };
        setReturnItems(
          [...returnItems, newReturnItem].sort((a, b) =>
            a.sales_return_number.localeCompare(b.sales_return_number)
          )
        );
        // Removed window.location.reload() to allow toast to show properly
        // Replace generateSalesReturnNumber() with empty string or fetch next number if needed
        setNewReturn({
          salesReturnNumber: "",
          invoiceOrBillNumber: "",
          customerName: "",
          type: "",
          items: [],
          refundMethod: "cash",
          remarks: "",
          status: "pending",
        });
        setItemForm({
          product_id: "",
          variant_id: "",
          quantity: "",
          selling_cost: 0,
          reason: "",
        });
        setSelectedProduct(null);
        setSelectedBatch(null);
        setInvoiceSearch("");
      }
    } catch (error) {
      const errorMsg =
        error.response?.data?.message ||
        (error.response?.data?.errors
          ? Object.values(error.response.data.errors).flat().join(", ")
          : "Error submitting sales return");
      toast.error(errorMsg);
      setErrors({ submit: errorMsg });
      if (error.response?.status === 401) {
        toast.error("Session expired. Please login again.");
      }
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteReturn = async (id) => {
    if (window.confirm("Are you sure you want to delete this sales return?")) {
      setLoading(true);
      try {
        await api.delete(`/api/sales-returns/${id}`);
        setReturnItems(returnItems.filter((item) => item.id !== id));
        setExpandedRows(expandedRows.filter((rowId) => rowId !== id));
        toast.success("Sales return deleted successfully!");
      } catch (error) {
        const errorMsg =
          error.response?.data?.message || "Error deleting sales return";
        toast.error(errorMsg);
      } finally {
        setLoading(false);
      }
    }
  };

  const handleApproveReturn = async (id) => {
    if (window.confirm("Are you sure you want to approve this sales return? This will update the stock quantities.")) {
      setLoading(true);
      try {
        const response = await api.post(`/api/sales-returns/${id}/approve`);
        toast.success("Sales return approved successfully!");
        
        // Update the return item in the list
        setReturnItems(returnItems.map((item) => 
          item.id === id ? { ...item, status: 'approved' } : item
        ));
      } catch (error) {
        const errorMsg = error.response?.data?.message || "Error approving sales return";
        toast.error(errorMsg);
      } finally {
        setLoading(false);
      }
    }
  };

  const toggleRowExpansion = (id) => {
    setExpandedRows((prev) =>
      prev.includes(id) ? prev.filter((rowId) => rowId !== id) : [...prev, id]
    );
  };

  const formatSellingCost = (cost) => {
    const parsedCost = parseFloat(cost);
    return isNaN(parsedCost) ? "" : parsedCost.toFixed(2);
  };

  const calculateTotalAmount = (items) => {
    return items
      .reduce((total, item) => {
        const cost = parseFloat(item.selling_cost) || 0;
        const quantity = parseFloat(item.quantity) || 0;
        return total + cost * quantity;
      }, 0)
      .toFixed(2);
  };

  return (
    <div className="min-h-screen bg-gray-100 p-6 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto">
        {/* Sales Return Form */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-6">
            {editMode ? "Edit Sales Return" : "Sales Return Form"}
            <span className="block text-sm font-normal text-gray-500 dark:text-gray-400 mt-1">
              {editMode
                ? "Update existing return details"
                : "Create a new sales return"}
            </span>
          </h2>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700">
            {loading && (
              <div className="flex flex-col items-center justify-center p-8 space-y-3">
                <div className="animate-spin rounded-full h-10 w-10 border-[3px] border-blue-500 border-t-transparent"></div>
                <p className="text-gray-500 dark:text-gray-400">
                  Processing your request...
                </p>
              </div>
            )}

            {Object.keys(errors).length > 0 && (
              <div className="p-3 mb-6 bg-red-50 text-red-600 dark:bg-red-900/30 dark:text-red-300 rounded-lg border border-red-100 dark:border-red-900/50 flex items-start">
                <FiAlertCircle className="flex-shrink-0 mt-0.5 mr-2" />
                <div>
                  <p className="font-medium">
                    Please fix the following issues:
                  </p>
                  <ul className="list-disc list-inside text-sm">
                    {Object.entries(errors).map(
                      ([key, value]) => value && <li key={key}>{value}</li>
                    )}
                  </ul>
                </div>
              </div>
            )}

            {!loading && (
              <div className="space-y-6">
                {/* Header Section */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
                  <div className="space-y-1">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Sales Return Number
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        name="salesReturnNumber"
                        value={newReturn.salesReturnNumber}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-700/50 text-gray-600 dark:text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                        disabled
                      />
                      <FiHash className="absolute right-3 top-2.5 text-gray-400" />
                    </div>
                  </div>

                  <div ref={invoiceSearchRef} className="space-y-1 relative">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Invoice/Bill Number
                    </label>
                    <div className="relative">
                      <input
                        ref={invoiceSearchInputRef}
                        type="text"
                        value={invoiceSearch}
                        onChange={(e) => setInvoiceSearch(e.target.value)}
                        onFocus={() =>
                          invoiceSearch && setShowInvoiceSuggestions(true)
                        }
                        onKeyDown={handleInvoiceSearchKeyDown}
                        placeholder="Search Invoice/Bill Number..."
                        className="w-full px-3 py-2 pl-9 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                        disabled={loading}
                      />
                      <FiSearch className="absolute left-3 top-2.5 text-gray-400" />
                    </div>

                    {showInvoiceSuggestions && (
                      <div className="absolute z-20 w-full mt-1">
                        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg overflow-hidden">
                          {filteredInvoices.length > 0 ? (
                            <ul className="max-h-60 overflow-y-auto">
                              {filteredInvoices.map((invoice, index) => (
                                <li
                                  key={`${invoice.type}-${invoice.id}`}
                                  onClick={() => handleSelectInvoice(invoice)}
                                  className={`px-4 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer flex justify-between items-center ${
                                    invoiceHighlightedIndex === index
                                      ? "bg-gray-50 dark:bg-gray-700"
                                      : ""
                                  }`}
                                >
                                  <span className="font-medium">
                                    {invoice.invoiceNumber}
                                  </span>
                                  <span className="text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 px-2 py-0.5 rounded-full">
                                    {invoice.type === "invoice"
                                      ? "Invoice"
                                      : "Sale"}
                                  </span>
                                </li>
                              ))}
                            </ul>
                          ) : (
                            <div className="px-4 py-3 text-center text-gray-500 dark:text-gray-400">
                              {invoiceSearch
                                ? "No matching invoices found"
                                : "Start typing to search"}
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="space-y-1">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Customer Name
                    </label>
                    <input
                      type="text"
                      name="customerName"
                      placeholder="Customer Name"
                      value={newReturn.customerName}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-700/50 text-gray-600 dark:text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                      disabled
                    />
                  </div>
                </div>

                {/* Bill Items Info */}
                {billItems.length > 0 && (
                  <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                    <div className="flex items-center gap-2">
                      <FiInfo className="text-blue-600 dark:text-blue-400" />
                      <span className="text-sm text-blue-800 dark:text-blue-200">
                        <strong>{billItems.length} items</strong> available for
                        return from this {selectedBillType}
                      </span>
                    </div>
                    <p className="text-xs text-blue-600 dark:text-blue-300 mt-1">
                      You can only return items that were sold in this{" "}
                      {selectedBillType}
                    </p>
                  </div>
                )}

                {/* Items Section */}
                <div className="mt-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                      Items to Return
                    </h3>
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {newReturn.items.length} item(s) added
                    </span>
                  </div>

                  {/* Add Item Form */}
                  <div className="bg-gray-50 dark:bg-gray-700/30 p-4 rounded-xl border border-gray-100 dark:border-gray-700 mb-6">
                    <div className="grid grid-cols-1 md:grid-cols-5 gap-3">
                      <div className="relative">
                        <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">
                          Select Product
                        </label>
                        <div className="relative">
                          <input
                            ref={productSelectRef}
                            type="text"
                            value={productSearchQuery}
                            onChange={handleProductSearchChange}
                            onKeyDown={handleProductKeyDown}
                            onFocus={() => {
                              // Don't show dropdown on focus - only show when typing or using arrow keys
                              // This prevents dropdown from appearing immediately when clicking the input
                            }}
                            onBlur={(e) => {
                              // Check if the blur is due to clicking on dropdown
                              const relatedTarget = e.relatedTarget;
                              if (
                                relatedTarget &&
                                relatedTarget.closest(".product-dropdown")
                              ) {
                                return; // Don't hide if clicking within dropdown
                              }
                              // Delay hiding dropdown to allow for clicks
                              setTimeout(
                                () => setShowProductDropdown(false),
                                200
                              );
                            }}
                            placeholder="Type to search products or press ↓ to browse..."
                            className="w-full px-3 py-2 text-sm border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                            disabled={loading}
                          />
                          <FiChevronDown className="absolute right-3 top-2.5 text-gray-400 pointer-events-none" />

                          {/* Dropdown below input */}
                          {showProductDropdown &&
                            filteredProducts.length > 0 && (
                              <div className="product-dropdown absolute z-50 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                                {filteredProducts.map((productBatch, index) => (
                                  <div
                                    key={
                                      productBatch.variant_id ||
                                      productBatch.product_id
                                    }
                                    onMouseDown={(e) => {
                                      e.preventDefault(); // Prevent input blur
                                      handleSelectProductBatch(productBatch);
                                    }}
                                    onClick={() =>
                                      handleSelectProductBatch(productBatch)
                                    }
                                    className={`px-3 py-2 text-sm cursor-pointer transition-colors ${
                                      index === productHighlightedIndex
                                        ? "bg-blue-100 dark:bg-blue-600 text-blue-900 dark:text-white"
                                        : "hover:bg-gray-100 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200"
                                    }`}
                                    onMouseEnter={() =>
                                      setProductHighlightedIndex(index)
                                    }
                                  >
                                    <div className="font-medium">
                                      {productBatch.display_name}
                                    </div>
                                    <div className="text-xs text-gray-500 dark:text-gray-400">
                                      {productBatch.max_return_quantity ? (
                                        <>
                                          Sold: {productBatch.quantity_sold} |
                                          Max Return:{" "}
                                          {productBatch.max_return_quantity} |
                                          Price: LKR{" "}
                                          {(() => {
                                            const price =
                                              productBatch.sales_price ||
                                              productBatch.unit_price ||
                                              0;
                                            return (
                                              typeof price === "number"
                                                ? price
                                                : parseFloat(price) || 0
                                            ).toFixed(2);
                                          })()}
                                        </>
                                      ) : (
                                        <>
                                          Stock: {productBatch.stock || 0} |
                                          Price: LKR{" "}
                                          {(() => {
                                            const price =
                                              productBatch.sales_price || 0;
                                            return (
                                              typeof price === "number"
                                                ? price
                                                : parseFloat(price) || 0
                                            ).toFixed(2);
                                          })()}
                                        </>
                                      )}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            )}
                        </div>
                      </div>

                      {/* Selected Product & Batch Summary */}
                      {selectedProduct && selectedBatch && (
                        <div className="md:col-span-5 bg-green-50 dark:bg-green-900/20 p-3 rounded-lg border border-green-200 dark:border-green-800">
                          <div className="flex items-center justify-between">
                            <div>
                              <span className="text-sm font-medium text-green-800 dark:text-green-200">
                                Selected: {selectedProduct.product_name} - Batch{" "}
                                {selectedBatch.batch_number}
                              </span>
                              <div className="text-xs text-green-600 dark:text-green-300 mt-1">
                                Barcode: {selectedBatch.barcode} | Expiry:{" "}
                                {selectedBatch.expiry_date || "N/A"} | Price:
                                LKR{" "}
                                {selectedBatch.sales_price?.toFixed(2) ||
                                  "0.00"}
                              </div>
                            </div>
                            <button
                              onClick={() => {
                                setSelectedProduct(null);
                                setSelectedBatch(null);
                                setItemForm({
                                  ...itemForm,
                                  product_id: "",
                                  variant_id: "",
                                  selling_cost: 0,
                                });
                              }}
                              className="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-200"
                            >
                              ✕
                            </button>
                          </div>
                        </div>
                      )}

                      <div>
                        <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">
                          Quantity
                        </label>
                        <input
                          ref={quantityInputRef}
                          type="number"
                          name="quantity"
                          value={itemForm.quantity}
                          onChange={handleItemFormChange}
                          onKeyDown={handleQuantityKeyDown}
                          placeholder="0"
                          className={`w-full px-3 py-2 text-sm border rounded-lg bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 focus:ring-2 focus:border-transparent transition-all ${
                            quantityError
                              ? "border-red-300 dark:border-red-600 focus:ring-red-500"
                              : "border-gray-200 dark:border-gray-700 focus:ring-blue-500"
                          }`}
                          disabled={loading}
                        />
                        {quantityError && (
                          <p className="mt-1 text-xs text-red-600 dark:text-red-400">
                            {quantityError}
                          </p>
                        )}
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">
                          Unit Price
                        </label>
                        <div className="relative">
                          <span className="absolute left-3 top-2 text-sm text-gray-400">
                            LKR
                          </span>
                          <input
                            ref={sellingCostInputRef}
                            type="number"
                            name="selling_cost"
                            value={itemForm.selling_cost}
                            onChange={handleItemFormChange}
                            onKeyDown={handleSellingCostKeyDown}
                            placeholder="0.00"
                            className="w-full px-3 py-2 pl-12 text-sm border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">
                          Reason (Optional)
                        </label>
                        <input
                          ref={reasonInputRef}
                          type="text"
                          name="reason"
                          value={itemForm.reason}
                          onChange={handleItemFormChange}
                          onKeyDown={handleReasonKeyDown}
                          placeholder="Damage, Wrong item, etc."
                          className="w-full px-3 py-2 text-sm border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                          disabled={loading}
                        />
                      </div>

                      <div className="flex items-end">
                        <button
                          onClick={handleAddItem}
                          onKeyDown={handleAddButtonKeyDown}
                          data-add-item-button
                          className={`w-full px-3 py-2.5 text-sm font-medium rounded-lg transition-all focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                            loading ||
                            !selectedProduct ||
                            !selectedBatch ||
                            itemForm.quantity <= 0
                              ? "bg-gray-100 dark:bg-gray-600 text-gray-400 cursor-not-allowed"
                              : "bg-blue-600 hover:bg-blue-700 text-white shadow-sm"
                          }`}
                          disabled={
                            loading ||
                            !selectedProduct ||
                            !selectedBatch ||
                            itemForm.quantity <= 0
                          }
                        >
                          Add Item
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Items Table */}
                  {newReturn.items.length > 0 ? (
                    <div className="overflow-hidden rounded-xl border border-gray-200 dark:border-gray-700 shadow-xs">
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                          <thead className="bg-gray-50 dark:bg-gray-700/50">
                            <tr>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Product & Batch
                              </th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Qty
                              </th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Unit Price
                              </th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Total
                              </th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Reason
                              </th>
                              <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Actions
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            {newReturn.items.map((item, index) => (
                              <tr
                                key={item.product_id + index}
                                className="hover:bg-gray-50 dark:hover:bg-gray-700/50"
                              >
                                <td className="px-4 py-3 text-sm font-medium text-gray-900 dark:text-white">
                                  <div>{item.product_name}</div>
                                  {item.batch_number && (
                                    <div className="text-xs text-gray-500 dark:text-gray-400">
                                      Batch: {item.batch_number}
                                      {item.expiry_date &&
                                        ` | Exp: ${item.expiry_date}`}
                                    </div>
                                  )}
                                </td>
                                <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                  {item.quantity}
                                </td>
                                <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                  LKR {formatSellingCost(item.selling_cost)}
                                </td>
                                <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                  LKR{" "}
                                  {formatSellingCost(
                                    item.quantity * item.selling_cost
                                  )}
                                </td>
                                <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                  {item.reason || "-"}
                                </td>
                                <td className="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                                  <button
                                    onClick={() => handleRemoveItem(index)}
                                    className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 transition-colors"
                                    disabled={loading}
                                  >
                                    <FiTrash2 className="inline mr-1" /> Remove
                                  </button>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                          <tfoot className="bg-gray-50 dark:bg-gray-700/50">
                            <tr>
                              <td
                                colSpan="3"
                                className="px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-300 text-right"
                              >
                                Subtotal:
                              </td>
                              <td className="px-4 py-3 text-sm font-bold text-gray-900 dark:text-white">
                                LKR {calculateTotalAmount(newReturn.items)}
                              </td>
                              <td colSpan="2"></td>
                            </tr>
                          </tfoot>
                        </table>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8 border-2 border-dashed border-gray-200 dark:border-gray-700 rounded-xl">
                      <FiPackage className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                        No items added
                      </h3>
                      <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        Search for products above to add return items
                      </p>
                    </div>
                  )}
                </div>

                {/* Footer Section */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Refund Method
                      </label>
                      <div className="relative">
                        <select
                          name="refundMethod"
                          value={newReturn.refundMethod}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none transition-all"
                          disabled={loading}
                        >
                          <option value="cash">Cash</option>
                          <option value="card">Card</option>
                          <option value="store-credit">Store Credit</option>
                        </select>
                        <FiChevronDown className="absolute right-3 top-2.5 text-gray-400 pointer-events-none" />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Status
                      </label>
                      <div className="relative">
                        <select
                          name="status"
                          value={newReturn.status}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none transition-all"
                          disabled={loading}
                        >
                          <option value="pending">Pending</option>
                          <option value="approved">Approved</option>
                          <option value="rejected">Rejected</option>
                        </select>
                        <FiChevronDown className="absolute right-3 top-2.5 text-gray-400 pointer-events-none" />
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Remarks
                    </label>
                    <textarea
                      name="remarks"
                      value={newReturn.remarks}
                      onChange={handleInputChange}
                      rows={3}
                      placeholder="Additional notes about this return..."
                      className="w-full px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                      disabled={loading}
                    />
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col-reverse md:flex-row justify-end space-y-4 space-y-reverse md:space-y-0 md:space-x-4 mt-8">
                  {editMode && (
                    <button
                      onClick={handleCancelEdit}
                      className="px-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    >
                      Cancel
                    </button>
                  )}
                  <button
                    onClick={handleSubmitReturn}
                    disabled={
                      loading ||
                      !newReturn.salesReturnNumber ||
                      !newReturn.invoiceOrBillNumber ||
                      !newReturn.customerName ||
                      newReturn.items.length === 0
                    }
                    className={`px-4 py-2.5 rounded-lg font-medium transition-colors ${
                      loading ||
                      !newReturn.salesReturnNumber ||
                      !newReturn.invoiceOrBillNumber ||
                      !newReturn.customerName ||
                      newReturn.items.length === 0
                        ? "bg-gray-200 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed"
                        : "bg-blue-600 hover:bg-blue-700 text-white shadow-sm"
                    }`}
                  >
                    {editMode ? (
                      <>
                        <FiSave className="inline mr-2" />
                        Update Return
                      </>
                    ) : (
                      <>
                        <FiCheckCircle className="inline mr-2" />
                        Submit Return
                      </>
                    )}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* View Return Modal */}
        {viewReturn && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg max-w-2xl w-full">
              <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">
                Sales Return Details
              </h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                    Sales Return Number
                  </label>
                  <p className="text-gray-900 dark:text-gray-200">
                    {viewReturn.sales_return_number || "N/A"}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                    Invoice/Bill Number
                  </label>
                  <p className="text-gray-900 dark:text-gray-200">
                    {viewReturn.invoice_no || viewReturn.bill_number || "N/A"}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                    Customer Name
                  </label>
                  <p className="text-gray-900 dark:text-gray-200">
                    {viewReturn.customer_name || "N/A"}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                    Items
                  </label>
                  <table className="min-w-full mt-2">
                    <thead>
                      <tr>
                        <th className="px-2 py-1 text-left text-sm font-medium text-gray-700 dark:text-gray-200">
                          Product
                        </th>
                        <th className="px-2 py-1 text-left text-sm font-medium text-gray-700 dark:text-gray-200">
                          Quantity
                        </th>
                        <th className="px-2 py-1 text-left text-sm font-medium text-gray-700 dark:text-gray-200">
                          Total Cost
                        </th>
                        <th className="px-2 py-1 text-left text-sm font-medium text-gray-700 dark:text-gray-200">
                          Reason
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {viewReturn.items.map((item, index) => (
                        <tr key={index}>
                          <td className="px-2 py-1 text-sm text-gray-700 dark:text-gray-200">
                            {item.product_name}
                          </td>
                          <td className="px-2 py-1 text-sm text-gray-700 dark:text-gray-200">
                            {item.quantity}
                          </td>
                          <td className="px-2 py-1 text-sm text-gray-700 dark:text-gray-200">
                            LKR{" "}
                            {formatSellingCost(
                              item.quantity * item.selling_cost
                            )}
                          </td>
                          <td className="px-2 py-1 text-sm text-gray-700 dark:text-gray-200">
                            {item.reason || "N/A"}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                    Total Amount
                  </label>
                  <p className="text-gray-900 dark:text-gray-200">
                    LKR {calculateTotalAmount(viewReturn.items)}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                    Refund Method
                  </label>
                  <p className="text-gray-900 dark:text-gray-200">
                    {viewReturn.refund_method || "N/A"}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                    Remarks
                  </label>
                  <p className="text-gray-900 dark:text-gray-200">
                    {viewReturn.remarks || "N/A"}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                    Status
                  </label>
                  <p className="text-gray-900 dark:text-gray-200">
                    {viewReturn.status || "N/A"}
                  </p>
                </div>
              </div>
              <div className="mt-6 flex justify-end">
                <button
                  onClick={handleCloseView}
                  className="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 transition duration-300"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Sales Return History */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-100">
              Sales Return History
            </h2>
            <div className="flex space-x-2">
              <button className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-colors duration-200">
                Export
              </button>
              <button className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg text-sm font-medium transition-colors duration-200">
                New Return
              </button>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
            {returnItems.length === 0 && !loading && (
              <div className="p-8 text-center">
                <div className="mx-auto w-24 h-24 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
                  <FiArchive className="text-gray-400 text-3xl" />
                </div>
                <h3 className="text-lg font-medium text-gray-500 dark:text-gray-300 mb-1">
                  No sales returns found
                </h3>
                <p className="text-gray-400 dark:text-gray-500 text-sm">
                  Create your first sales return to get started
                </p>
              </div>
            )}

            {returnItems.length > 0 && (
              <div className="overflow-x-auto">
                <table className="w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-12"
                      ></th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                      >
                        Return #
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                      >
                        Invoice
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                      >
                        Customer
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                      >
                        Amount
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                      >
                        Method
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                      >
                        Status
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                      >
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {returnItems.map((returnItem) => (
                      <React.Fragment key={returnItem.id}>
                        <tr className="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-150">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <button
                              onClick={() => toggleRowExpansion(returnItem.id)}
                              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors duration-200"
                              disabled={loading}
                            >
                              {expandedRows.includes(returnItem.id) ? (
                                <FiChevronUp className="h-5 w-5" />
                              ) : (
                                <FiChevronDown className="h-5 w-5" />
                              )}
                            </button>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="text-sm font-medium text-gray-900 dark:text-white">
                              {returnItem.sales_return_number || "N/A"}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="text-sm text-gray-500 dark:text-gray-400">
                              {returnItem.invoice_no ||
                                returnItem.bill_number ||
                                "N/A"}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                                <FiUser className="text-blue-600 dark:text-blue-400" />
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900 dark:text-white">
                                  {returnItem.customer_name || "N/A"}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="text-sm font-semibold text-gray-900 dark:text-white">
                              LKR {calculateTotalAmount(returnItem.items)}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
                              {returnItem.refund_method || "N/A"}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span
                              className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                returnItem.status === "Completed"
                                  ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                                  : returnItem.status === "Pending"
                                    ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
                                    : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
                              }`}
                            >
                              {returnItem.status || "N/A"}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div className="flex justify-end space-x-3">
                              {returnItem.status === 'pending' && (
                                <button
                                  onClick={() => handleApproveReturn(returnItem.id)}
                                  className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 transition-colors duration-200"
                                  disabled={loading}
                                  title="Approve Return"
                                >
                                  <FiCheck className="h-5 w-5" />
                                </button>
                              )}
                              <button
                                onClick={() => handleViewReturn(returnItem)}
                                className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 transition-colors duration-200"
                                disabled={loading}
                              >
                                <FiEye className="h-5 w-5" />
                              </button>
                              <button
                                onClick={() => handleEditReturn(returnItem)}
                                className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200 transition-colors duration-200"
                                disabled={loading}
                              >
                                <FiEdit2 className="h-5 w-5" />
                              </button>
                              <button
                                onClick={() =>
                                  handleDeleteReturn(returnItem.id)
                                }
                                className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 transition-colors duration-200"
                                disabled={loading}
                              >
                                <FiTrash2 className="h-5 w-5" />
                              </button>
                            </div>
                          </td>
                        </tr>
                        {expandedRows.includes(returnItem.id) && (
                          <tr>
                            <td
                              colSpan="8"
                              className="px-6 py-4 bg-gray-50 dark:bg-gray-700/30"
                            >
                              <div className="space-y-4">
                                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                  Return Items
                                </h4>
                                <div className="overflow-hidden border border-gray-200 dark:border-gray-700 rounded-lg">
                                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                    <thead className="bg-gray-100 dark:bg-gray-700">
                                      <tr>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">
                                          Product
                                        </th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">
                                          Quantity
                                        </th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">
                                          Unit Price
                                        </th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">
                                          Total
                                        </th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">
                                          Reason
                                        </th>
                                      </tr>
                                    </thead>
                                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                      {returnItem.items.map((item, index) => (
                                        <tr
                                          key={index}
                                          className="hover:bg-gray-50 dark:hover:bg-gray-700/50"
                                        >
                                          <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                            {item.product_name}
                                          </td>
                                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            {item.quantity}
                                          </td>
                                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            LKR{" "}
                                            {formatSellingCost(
                                              item.selling_cost
                                            )}
                                          </td>
                                          <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                            LKR{" "}
                                            {formatSellingCost(
                                              item.quantity * item.selling_cost
                                            )}
                                          </td>
                                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                              {item.reason || "Not specified"}
                                            </span>
                                          </td>
                                        </tr>
                                      ))}
                                    </tbody>
                                  </table>
                                </div>
                              </div>
                            </td>
                          </tr>
                        )}
                      </React.Fragment>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SalesReturn;
