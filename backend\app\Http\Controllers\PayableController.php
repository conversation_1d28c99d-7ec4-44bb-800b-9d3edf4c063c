<?php

namespace App\Http\Controllers;

use App\Models\Purchase;
use App\Models\Payment;
use App\Models\ChequeStatement;
use App\Models\Supplier;
use App\Models\PurchaseReturn;
use App\Models\PurchaseReturnItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PayableController extends Controller
{
    public function index(Request $request)
    {
        try {
            $supplierId = $request->query('supplier_id');
            $purchases = Purchase::select('id', 'supplier_id', 'bill_number', 'total', 'paid_amount', 'date_of_purchase', 'payment_method')
                ->with(['supplier' => function ($query) {
                    $query->select('id', 'supplier_name', 'opening_balance');
                }])
                ->when($supplierId, function ($query, $supplierId) {
                    return $query->where('supplier_id', $supplierId);
                })
                ->get()
                ->map(function ($purchase) {
                    $pendingAmount = (float) $purchase->total - (float) $purchase->paid_amount;

                    // Calculate approved purchase return amount for this purchase
                    $purchaseReturnAmount = $this->getApprovedPurchaseReturnAmount($purchase->id, $purchase->payment_method);
                    
                    // Subtract purchase return amount from pending amount
                    $finalPendingAmount = $pendingAmount - $purchaseReturnAmount;

                    $paymentHistory = Payment::where('transaction_type', 'purchase')
                        ->where('transaction_id', $purchase->id)
                        ->orderBy('payment_date', 'asc')
                        ->get(['amount', 'payment_date', 'payment_method', 'reference_no'])
                        ->map(function ($payment) {
                            return [
                                'amount' => (float) $payment->amount,
                                'payment_date' => $payment->payment_date ? $payment->payment_date->toDateString() : null,
                                'payment_method' => $payment->payment_method,
                                'reference_no' => $payment->reference_no,
                            ];
                        });

                    return [
                        'id' => $purchase->id,
                        'type' => 'purchase',
                        'supplier_id' => $purchase->supplier_id,
                        'supplier_name' => $purchase->supplier ? $purchase->supplier->supplier_name : 'Unknown Supplier',
                        'bill_number' => $purchase->bill_number,
                        'reference_no' => $purchase->bill_number,
                        'total_amount' => (float) $purchase->total,
                        'final_outstanding_amount' => $finalPendingAmount,
                        'paid_amount' => (float) $purchase->paid_amount,
                        'purchase_return_amount' => $purchaseReturnAmount,
                        'payment_history' => $paymentHistory,
                        'previous_outstanding_balance' => 0.0,
                        'total_credits' => 0.0,
                        'date' => $purchase->date_of_purchase ? $purchase->date_of_purchase->toDateString() : null,
                        'payment_type' => $purchase->payment_method,
                        'status' => $finalPendingAmount > 0 ? 'Pending' : 'Paid',
                    ];
                })->filter(function ($purchase) {
                    return $purchase['final_outstanding_amount'] > 0;
                });

            $payable = $purchases->sortByDesc('date')->values();

            // Add supplier's opening balance as a transaction if supplier_id is provided
            if ($supplierId) {
                $supplier = Supplier::find($supplierId);
                if ($supplier && $supplier->opening_balance > 0) {
                    // Calculate settled opening balance for this supplier
                    $settledOpeningBalance = Payment::where('refer_type', 'Supplier')
                        ->where('refer_id', $supplierId)
                        ->where('transaction_type', 'opening_balance')
                        ->sum('opening_balance');

                    $remainingOpeningBalance = (float) $supplier->opening_balance - (float) $settledOpeningBalance;

                    if ($remainingOpeningBalance > 0) {
                        $payable->push([
                            'id' => 'opening_balance_' . $supplierId,
                            'type' => 'opening_balance',
                            'supplier_id' => $supplier->id,
                            'supplier_name' => $supplier->supplier_name,
                            'bill_number' => null,
                            'reference_no' => 'OPENING-' . $supplierId,
                            'total_amount' => (float) $supplier->opening_balance,
                            'final_outstanding_amount' => $remainingOpeningBalance,
                            'paid_amount' => (float) $settledOpeningBalance,
                            'purchase_return_amount' => 0.0,
                            'payment_history' => [],
                            'previous_outstanding_balance' => 0.0,
                            'total_credits' => 0.0,
                            'date' => null,
                            'payment_type' => null,
                            'status' => 'Pending',
                        ]);
                    }
                }
            }

            // Calculate total pending amount
            $totalPendingAmount = $payable->sum('final_outstanding_amount');

            return response()->json([
                'transactions' => $payable->toArray(), // Convert Collection to array
                'total_pending_amount' => $totalPendingAmount,
                'supplier_opening_balance' => $supplierId && $supplier ? (float) $supplier->opening_balance : 0.0,
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching payable transactions: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return response()->json(['error' => 'Failed to fetch payable transactions.'], 500);
        }
    }

    /**
     * Get approved purchase return amount for a specific purchase
     * Only consider returns if the original purchase was made with credit or cheque payment method
     */
    private function getApprovedPurchaseReturnAmount($purchaseId, $paymentMethod)
    {
        // Only consider purchase returns if the original purchase was made with credit or cheque
        if (!in_array(strtolower($paymentMethod), ['credit', 'cheque'])) {
            return 0;
        }

        // Get the specific purchase
        $purchase = Purchase::find($purchaseId);
        if (!$purchase) {
            return 0;
        }

        $supplierId = $purchase->supplier_id;

        // Get approved purchase returns for this supplier
        // Note: Since purchase returns are not directly linked to specific purchases,
        // we calculate the total return amount for the supplier and apply it proportionally
        $purchaseReturns = PurchaseReturn::where('supplier_id', $supplierId)
            ->where('status', 'approved')
            ->get();

        $totalReturnAmount = 0;

        foreach ($purchaseReturns as $purchaseReturn) {
            // Calculate return amount for this purchase return
            $returnAmount = PurchaseReturnItem::where('purchase_return_id', $purchaseReturn->id)
                ->get()
                ->sum(function($item) {
                    return $item->quantity * $item->buying_cost;
                });

            $totalReturnAmount += $returnAmount;
        }

        // For now, we'll return the total return amount for the supplier
        // In a more sophisticated implementation, you might want to link returns to specific purchases
        return $totalReturnAmount;
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'paid_amount' => 'required|numeric|min:0',
            'payment_date' => 'nullable|date',
            'payment_method' => 'required|string|in:Cash,Card,Cheque,Online Payment',
            'cheque_no' => 'nullable|string|required_if:payment_method,Cheque',
            'bank_name' => 'nullable|string|required_if:payment_method,Cheque',
            'bank' => 'nullable|string',
            'issue_date' => 'nullable|date|required_if:payment_method,Cheque',
            'note' => 'nullable|string',
            'reference_no' => 'nullable|string',
            'transaction_type' => 'required|string|in:purchase,opening_balance',
            'opening_balance' => 'nullable|numeric|min:0',
            'supplier_id' => 'required|exists:suppliers,id',
            'discount' => 'nullable|numeric|min:0',
        ]);

        try {
            $paymentMethod = $request->input('payment_method');
            $chequeNo = $request->input('cheque_no');
            $bankName = $request->input('bank_name');
            $issueDate = $request->input('issue_date');
            $note = $request->input('note');
            $transactionType = $request->input('transaction_type');
            $newPaidAmount = (float) $request->input('paid_amount');
            $openingBalance = (float) ($request->input('opening_balance') ?? 0);
            $referenceNo = $request->input('reference_no');
            $supplierId = $request->input('supplier_id');
            $discount = (float) ($request->input('discount') ?? 0);

            $supplier = Supplier::find($supplierId);
            if (!$supplier) {
                return response()->json(['error' => 'Supplier not found.'], 404);
            }

            if ($transactionType === 'purchase') {
                $purchase = Purchase::find($id);
                if (!$purchase) {
                    return response()->json(['error' => 'Purchase not found.'], 404);
                }

                $total = (float) $purchase->total;
                $currentPaid = (float) $purchase->paid_amount;
                $updatedPaid = $currentPaid + $newPaidAmount;
                $balance = $total - $updatedPaid;

                if ($balance < 0) {
                    return response()->json(['error' => 'Paid amount exceeds outstanding balance.'], 400);
                }

                $purchase->paid_amount = $updatedPaid;
                $purchase->status = $balance <= 0 ? 'paid' : 'pending';
                $purchase->updated_at = now();
                $purchase->save();
            } else {
                // Validate that opening balance payment doesn't exceed remaining opening balance
                $settledOpeningBalance = Payment::where('refer_type', 'Supplier')
                    ->where('refer_id', $supplierId)
                    ->where('transaction_type', 'opening_balance')
                    ->sum('opening_balance');

                $remainingOpeningBalance = (float) $supplier->opening_balance - (float) $settledOpeningBalance;
                if ($openingBalance > $remainingOpeningBalance) {
                    return response()->json(['error' => 'Opening balance payment exceeds remaining opening balance.'], 400);
                }
            }

            // Get bank ledger name if bank account is selected for Online, Cheque, or Card payments
            $bankLedgerName = null;
            if (in_array($paymentMethod, ['Card', 'Cheque', 'Online Payment'])) {
                $bankAccountInfo = $request->input('bank');
                if (!empty($bankAccountInfo)) {
                    $bankAccountParts = explode('-', $bankAccountInfo);

                    if (count($bankAccountParts) === 2) {
                        $bankAccountType = $bankAccountParts[0]; // 'staff_ledger' or 'sub_group'
                        $bankAccountId = $bankAccountParts[1];

                        if ($bankAccountType === 'staff_ledger') {
                            $staffLedger = \App\Models\StaffLedger::find($bankAccountId);
                            $bankLedgerName = $staffLedger ? $staffLedger->name : null;
                        } else {
                            $subGroup = \App\Models\AccountSubGroup::find($bankAccountId);
                            $bankLedgerName = $subGroup ? $subGroup->sub_group_name : null;
                        }
                    }
                }
            }

            $payment = Payment::create([
                'voucher_no' => Payment::getNextPaymentVoucherNumber(),
                'transaction_type' => $transactionType,
                'transaction_id' => $transactionType === 'purchase' ? $id : null,
                'reference_no' => $referenceNo,
                'refer_type' => 'Supplier',
                'refer_id' => $supplierId,
                'refer_name' => $supplier->supplier_name,
                'amount' => $newPaidAmount,
                'opening_balance' => $openingBalance,
                'discount' => $discount,
                'payment_date' => $request->input('payment_date') ?? now(),
                'payment_method' => $paymentMethod,
                'cheque_no' => $paymentMethod === 'Cheque' ? $chequeNo : null,
                'bank_name' => $paymentMethod === 'Cheque' ? $bankName : null,
                'bank' => $bankLedgerName,
                'issue_date' => $paymentMethod === 'Cheque' ? $issueDate : null,
                'note' => $note,
            ]);

            // Log user activity for payment voucher creation
            $userId = auth()->id();
            if ($userId) {
                \App\Http\Controllers\UserActivityController::logActivity(
                    $userId,
                    'Payment Voucher Created',
                    'Payment Vouchers',
                    $payment->id,
                    [
                        'voucher_no' => $payment->voucher_no,
                        'transaction_type' => $payment->transaction_type,
                        'amount' => $payment->amount,
                        'payment_method' => $payment->payment_method,
                        'supplier_id' => $supplierId,
                        'supplier_name' => $supplier->supplier_name
                    ]
                );
            } else {
                \Log::error('Cannot log Payment Voucher Created - no authenticated user found');
            }

            // If payment is being soft deleted, set deleted_by
            if ($request->has('delete') && $request->boolean('delete')) {
                $payment->deleted_by = $request->user()->id ?? auth()->id();
                $payment->save();
                $payment->delete();
            }

            if (strtolower($paymentMethod) === 'cheque') {
                ChequeStatement::create([
                    'payment_id' => $payment->id,
                    'voucher_no' => $payment->voucher_no,
                    'transaction_id' => $payment->transaction_id,
                    'transaction_type' => $payment->transaction_type,
                    'reference_no' => $payment->reference_no,
                    'refer_type' => $payment->refer_type,
                    'refer_id' => $payment->refer_id,
                    'refer_name' => $payment->refer_name,
                    'amount' => $payment->amount,
                    'payment_date' => $payment->payment_date,
                    'cheque_no' => $payment->cheque_no,
                    'bank_name' => $payment->bank_name,
                    'issue_date' => $payment->issue_date,
                    'note' => $payment->note,
                    'status' => 'pending'
                ]);
            }

            return response()->json([
                'message' => 'Payment processed successfully.',
                'status' => $transactionType === 'purchase' ? $purchase->status : 'pending',
                'balance' => $transactionType === 'purchase' ? $balance : null,
            ]);
        } catch (\Exception $e) {
            Log::error('Error processing payment: ' . $e->getMessage() . "\n" . $e->getTraceAsString() . "\nRequest Data: " . json_encode($request->all()));
            return response()->json(['error' => 'Failed to process payment.'], 500);
        }
    }

    public function deleted()
    {
        $vouchers = Payment::onlyTrashed()->with('deletedByUser')->where('voucher_no', 'like', 'PAY-%')->orderByDesc('id')->get();
        return response()->json(['success' => true, 'data' => $vouchers->map(function($voucher) {
            return array_merge($voucher->toArray(), [
                'deleted_by_user' => $voucher->deletedByUser ? [
                    'id' => $voucher->deletedByUser->id,
                    'name' => $voucher->deletedByUser->name,
                    'email' => $voucher->deletedByUser->email,
                ] : null,
            ]);
        })]);
    }
}