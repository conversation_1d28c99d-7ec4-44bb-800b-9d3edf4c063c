<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\Supplier;
use App\Models\StaffLedger;
use App\Models\PaymentMethod;
use App\Models\Payment;
use App\Models\ChequeStatement;
use App\Models\AccountSubGroup;
use App\Models\PurchaseReturn;
use App\Models\PurchaseReturnItem;
use App\Models\Purchase;
use Illuminate\Http\Request;
use Carbon\Carbon;
use App\Models\SalesReturn;
use App\Models\SalesReturnItem;

class StatementController extends Controller
{
    public function generateStatement(Request $request)
    {
        $request->validate([
            'person_type' => 'required|in:Customer,Supplier,Other',
            'person_id' => 'required',
            'from_date' => 'required|date',
            'to_date' => 'required|date|after_or_equal:from_date',
        ]);

        $personType = $request->person_type;
        $personId = $request->person_id;
        $fromDate = Carbon::parse($request->from_date)->startOfDay();
        $toDate = Carbon::parse($request->to_date)->endOfDay();

        try {
            // Get person details
            $person = $this->getPersonDetails($personType, $personId);
            if (!$person) {
                return response()->json(['message' => 'Person not found'], 404);
            }

            // Get opening balance and past year balance
            $openingBalanceDetails = $this->getOpeningBalanceDetails($person, $personType, $fromDate);
            
            // Get transactions from PaymentMethod table
            $paymentMethodTransactions = $this->getPaymentMethodTransactions($personType, $personId, $fromDate, $toDate);
            
            // Get transactions from Payment table (vouchers)
            $paymentTransactions = $this->getPaymentTransactions($personType, $personId, $fromDate, $toDate);

            // Get declined cheque statement transactions
            $declinedChequeTransactions = $this->getDeclinedChequeTransactions($personType, $personId, $fromDate, $toDate);

            // Get approved sales return transactions (credit/cheque only)
            $salesReturnTransactions = $this->getApprovedSalesReturnTransactions($personType, $personId, $fromDate, $toDate);

            // Get approved purchase return transactions (credit/cheque only) for suppliers
            $purchaseReturnTransactions = $this->getApprovedPurchaseReturnTransactions($personType, $personId, $fromDate, $toDate);

            // Combine and sort transactions
            $allTransactions = collect($paymentMethodTransactions)
                ->merge($paymentTransactions)
                ->merge($declinedChequeTransactions)
                ->merge($salesReturnTransactions)
                ->merge($purchaseReturnTransactions)
                ->sortBy('date')
                ->values();

            // Calculate running balance
            $transactions = $this->calculateRunningBalance($allTransactions, $openingBalanceDetails['total_opening_balance'], $personType, $person);

            return response()->json([
                'success' => true,
                'person' => $person,
                'transactions' => $transactions,
                'opening_balance' => $openingBalanceDetails['total_opening_balance'],
                'opening_balance_details' => [
                    'initial_opening_balance' => $openingBalanceDetails['initial_opening_balance'],
                    'past_year_balance' => $openingBalanceDetails['past_year_balance'],
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error generating statement: ' . $e->getMessage()
            ], 500);
        }
    }

    private function getPersonDetails($personType, $personId)
    {
        switch ($personType) {
            case 'Customer':
                return Customer::find($personId);
            case 'Supplier':
                return Supplier::find($personId);
            case 'Other':
                return StaffLedger::where('staff_id', $personId)->first();
            default:
                return null;
        }
    }

    private function getOpeningBalanceDetails($person, $personType, $fromDate)
    {
        $initialOpeningBalance = $this->getOpeningBalance($person, $personType);
        $pastYearBalance = $this->calculatePastYearBalance($personType, $person, $fromDate);

        return [
            'initial_opening_balance' => $initialOpeningBalance,
            'past_year_balance' => $pastYearBalance,
            'total_opening_balance' => $initialOpeningBalance + $pastYearBalance,
        ];
    }

    private function getOpeningBalance($person, $personType)
    {
        switch ($personType) {
            case 'Customer':
                return $person->openingbalance ?? 0;
            case 'Supplier':
                return $person->opening_balance ?? 0;
            case 'Other':
                return $person->opening_balance ?? 0;
            default:
                return 0;
        }
    }

    private function calculatePastYearBalance($personType, $person, $fromDate)
    {
        $startOfYear = Carbon::createFromDate($fromDate->year - 1, 1, 1)->startOfDay();
        $endOfPreviousYear = Carbon::createFromDate($fromDate->year - 1, 12, 31)->endOfDay();

        $personId = $personType === 'Other' ? ($person->staff_id ?? $person->id) : $person->id;

        // Get transactions from PaymentMethod table
        $paymentMethodTransactions = $this->getPaymentMethodTransactions($personType, $personId, $startOfYear, $endOfPreviousYear);
        
        // Get transactions from Payment table (vouchers and opening balance)
        $paymentTransactions = $this->getPaymentTransactions($personType, $personId, $startOfYear, $endOfPreviousYear);

        // Get declined cheque statement transactions
        $declinedChequeTransactions = $this->getDeclinedChequeTransactions($personType, $personId, $startOfYear, $endOfPreviousYear);

        // Get approved sales return transactions (credit/cheque only)
        $salesReturnTransactions = $this->getApprovedSalesReturnTransactions($personType, $personId, $startOfYear, $endOfPreviousYear);

        // Get approved purchase return transactions (credit/cheque only) for suppliers
        $purchaseReturnTransactions = $this->getApprovedPurchaseReturnTransactions($personType, $personId, $startOfYear, $endOfPreviousYear);

        // Combine transactions
        $allTransactions = collect($paymentMethodTransactions)
            ->merge($paymentTransactions)
            ->merge($declinedChequeTransactions)
            ->merge($salesReturnTransactions)
            ->merge($purchaseReturnTransactions)
            ->sortBy('date')
            ->values();

        // Calculate balance
        $balance = 0;
        foreach ($allTransactions as $transaction) {
            $balance += $transaction['debit'] - $transaction['credit'];
        }

        // Adjust balance based on person type
        if ($personType === 'Customer') {
            return $balance;
        } elseif ($personType === 'Supplier') {
            return -$balance; // Suppliers have credit balance
        } elseif ($personType === 'Other') {
            $mainAccountType = $this->getMainAccountType($person->account_group);
            $isDebitAccount = $this->isDebitAccountType($mainAccountType);
            return $isDebitAccount ? $balance : -$balance;
        }

        return $balance;
    }

    private function getMainAccountType($accountGroup)
    {
        $mainAccountGroups = [
            'Direct Expenses', 'Indirect Expenses', 'Indirect Income', 'Loan Liabilities',
            'Bank OD', 'Current Liabilities', 'Sundry Creditors', 'Capital Account',
            'Bank Accounts', 'Current Asset', 'Sundry Debtors', 'Fixed Asset', 'Stock in hand',
            'Purchase Account', 'Sales Accounts', 'Cash in Hand'
        ];

        if (in_array($accountGroup, $mainAccountGroups)) {
            return $accountGroup;
        }

        $subGroup = AccountSubGroup::where('sub_group_name', $accountGroup)->first();
        return $subGroup ? $subGroup->main_group : 'Direct Expenses';
    }

    private function isDebitAccountType($mainAccountType)
    {
        $debitAccountTypes = [
            'Direct Expenses', 'Indirect Expenses', 'Bank Accounts',
            'Current Asset', 'Sundry Debtors', 'Fixed Asset', 'Stock in hand',
            'Purchase Account', 'Cash in Hand'
        ];

        return in_array($mainAccountType, $debitAccountTypes);
    }

    private function getPaymentMethodTransactions($personType, $personId, $fromDate, $toDate)
    {
        $referType = $personType;

        if ($personType === 'Other') {
            // For bank account ledgers, get transactions where bank field matches the ledger name
            $staffLedger = StaffLedger::where('staff_id', $personId)->first();
            if (!$staffLedger) {
                return collect([]);
            }

            $query = PaymentMethod::where('bank', $staffLedger->name)
                ->whereBetween('date', [$fromDate, $toDate]);
        } else {
            $query = PaymentMethod::where('refer_type', $referType)
                ->where('refer_id', $personId)
                ->whereBetween('date', [$fromDate, $toDate]);

            // For Customer and Supplier, include both credit and cheque payment methods
            if (in_array($personType, ['Customer', 'Supplier'])) {
                $query->whereIn('payment_type', ['credit', 'cheque']);
            }
        }

        $transactions = $query->get();

        return $transactions->map(function ($transaction) use ($personType) {
            $debit = 0;
            $credit = 0;

            if ($personType === 'Customer') {
                if (in_array(strtolower($transaction->payment_type), ['credit', 'cheque'])) {
                    $credit = $transaction->settled_amount;
                    $debit = abs($transaction->total);
                }
            } elseif ($personType === 'Supplier') {
                if (in_array(strtolower($transaction->payment_type), ['credit', 'cheque'])) {
                    $debit = $transaction->settled_amount;
                    $credit = $transaction->total;
                }
            } elseif ($personType === 'Other') {
                // For bank account ledgers in payment_method table
                if (in_array(strtolower($transaction->type), ['sales', 'invoice'])) {
                    // Sales/Invoice type → settled amount in debit section
                    $debit = $transaction->settled_amount;
                } elseif (strtolower($transaction->type) === 'purchase') {
                    // Purchase type → settled amount in credit section
                    $credit = $transaction->settled_amount;
                } else {
                    // For other types, use existing logic
                    if (in_array(strtolower($transaction->payment_type), ['credit', 'cheque'])) {
                        $debit = $transaction->settled_amount;
                        $credit = abs($transaction->total);
                    } else {
                        $debit = $transaction->total;
                    }
                }
            }

            return [
                'date' => $transaction->date->format('Y-m-d'),
                'type' => ucfirst($transaction->type),
                'description' => $this->getDescription(strtolower($transaction->type), $transaction->reference_number),
                'debit' => $debit,
                'credit' => $credit,
                'balance' => 0,
            ];
        })->toArray();
    }

    private function getPaymentTransactions($personType, $personId, $fromDate, $toDate)
    {
        $query = Payment::whereBetween('payment_date', [$fromDate, $toDate]);
        $staffLedger = null;

        if ($personType === 'Other') {
            $staffLedger = StaffLedger::where('staff_id', $personId)->first();
            if (!$staffLedger) {
                return collect([]);
            }

            // Check if this is a bank account ledger
            $mainAccountType = $this->getMainAccountType($staffLedger->account_group);
            $isBankAccount = ($mainAccountType === 'Bank Accounts');

            if ($isBankAccount) {
                // For bank account ledgers, get transactions where bank field matches the ledger name
                $query->where('bank', $staffLedger->name);
            } else {
                // For other ledgers, use the existing logic
                $query->where('refer_type', 'Ledger')
                      ->where('refer_id', $staffLedger->id);
            }
        } else {
            $query->where('refer_type', $personType)
                  ->where('refer_id', $personId);
        }

        $transactions = $query->get();

        return $transactions->map(function ($payment) use ($personType, $staffLedger) {
            $debit = 0;
            $credit = 0;
            $type = '';
            $voucherNo = $payment->voucher_no;

            if ($personType === 'Other' && $staffLedger) {
                $mainAccountType = $this->getMainAccountType($staffLedger->account_group);
                $isBankAccount = ($mainAccountType === 'Bank Accounts');

                if ($isBankAccount) {
                    // For bank account ledgers in payments table
                    if ($payment->voucher_no && strpos($payment->voucher_no, 'PAY-') === 0) {
                        // Payment voucher type → amount in credit section
                        $credit = abs((float)$payment->amount);
                        $type = 'Payment Voucher';
                    } elseif ($payment->voucher_no && strpos($payment->voucher_no, 'REC-') === 0) {
                        // Receive voucher type → amount in debit section
                        $debit = abs((float)$payment->amount);
                        $type = 'Receive Voucher';
                    } else {
                        // Only include actual vouchers
                        return null;
                    }
                } else {
                    // For non-bank account ledgers, use existing logic
                    if ($payment->voucher_no && strpos($payment->voucher_no, 'PAY-') === 0) {
                        $debit = abs((float)$payment->amount);
                        $type = 'Payment Voucher';
                    } elseif ($payment->voucher_no && strpos($payment->voucher_no, 'REC-') === 0) {
                        $credit = abs((float)$payment->amount);
                        $type = 'Receive Voucher';
                    } else {
                        // Only include actual vouchers
                        return null;
                    }
                }
            } else {
                if ($payment->voucher_no && strpos($payment->voucher_no, 'PAY-') === 0) {
                    $debit = abs((float)$payment->amount);
                    $type = 'Payment Voucher';
                } elseif ($payment->voucher_no && strpos($payment->voucher_no, 'REC-') === 0) {
                    $credit = abs((float)$payment->amount);
                    $type = 'Receive Voucher';
                } else {
                    // Only include actual vouchers
                    return null;
                }
            }

            return [
                'date' => $payment->payment_date ? $payment->payment_date->format('Y-m-d') : '',
                'type' => $type,
                'description' => $this->getVoucherDescription($type, $voucherNo),
                'debit' => $debit,
                'credit' => $credit,
                'balance' => 0,
            ];
        })->filter()->values()->toArray();
    }

    private function getDeclinedChequeTransactions($personType, $personId, $fromDate, $toDate)
    {
        $query = ChequeStatement::whereBetween('payment_date', [$fromDate, $toDate])
            ->where('status', 'declined');

        $staffLedger = null;
        if ($personType === 'Other') {
            $staffLedger = StaffLedger::where('staff_id', $personId)->first();
            if (!$staffLedger) {
                return collect([]);
            }

            // For bank account ledgers, get declined cheques where bank_name matches the ledger name
            $mainAccountType = $this->getMainAccountType($staffLedger->account_group);
            $isBankAccount = ($mainAccountType === 'Bank Accounts');

            if ($isBankAccount) {
                $query->where('bank_name', $staffLedger->name);
            } else {
                $query->where('refer_type', 'Ledger')
                      ->where('refer_id', $staffLedger->id);
            }
        } else {
            $query->where('refer_type', $personType)
                  ->where('refer_id', $personId);
        }

        $declinedCheques = $query->get();

        return $declinedCheques->map(function ($cheque) use ($personType, $staffLedger) {
            $debit = 0;
            $credit = 0;
            $type = '';

            if ($personType === 'Customer') {
                // For customers: declined cheques increase their outstanding balance (debit)
                if (in_array($cheque->transaction_type, ['sale', 'invoice'])) {
                    $debit = $cheque->amount;
                    $type = 'Declined Cheque - ' . ucfirst($cheque->transaction_type);
                } elseif ($cheque->transaction_type === 'opening_balance') {
                    $debit = $cheque->amount;
                    $type = 'Declined Cheque - Opening Balance';
                } elseif ($cheque->voucher_no && strpos($cheque->voucher_no, 'REC-') === 0) {
                    $debit = $cheque->amount;
                    $type = 'Declined Cheque - Receive Voucher';
                }
            } elseif ($personType === 'Supplier') {
                // For suppliers: declined cheques increase their payable balance (credit)
                if ($cheque->transaction_type === 'purchase') {
                    $credit = $cheque->amount;
                    $type = 'Declined Cheque - Purchase';
                } elseif ($cheque->transaction_type === 'opening_balance') {
                    $credit = $cheque->amount;
                    $type = 'Declined Cheque - Opening Balance';
                } elseif ($cheque->voucher_no && strpos($cheque->voucher_no, 'PAY-') === 0) {
                    $credit = $cheque->amount;
                    $type = 'Declined Cheque - Payment Voucher';
                }
            } elseif ($personType === 'Other' && $staffLedger) {
                $mainAccountType = $this->getMainAccountType($staffLedger->account_group);
                $isBankAccount = ($mainAccountType === 'Bank Accounts');

                if ($isBankAccount) {
                    // For bank account ledgers: declined cheques affect the bank balance
                    if ($cheque->voucher_no && strpos($cheque->voucher_no, 'PAY-') === 0) {
                        // Payment voucher declined: amount goes to credit (bank didn't pay out)
                        $credit = $cheque->amount;
                        $type = 'Declined Cheque - Payment Voucher';
                    } elseif ($cheque->voucher_no && strpos($cheque->voucher_no, 'REC-') === 0) {
                        // Receive voucher declined: amount goes to debit (bank didn't receive)
                        $debit = $cheque->amount;
                        $type = 'Declined Cheque - Receive Voucher';
                    } elseif (in_array($cheque->transaction_type, ['sale', 'invoice'])) {
                        // Sales/Invoice type declined: amount goes to credit (bank didn't receive)
                        $credit = $cheque->amount;
                        $type = 'Declined Cheque - ' . ucfirst($cheque->transaction_type);
                    } elseif ($cheque->transaction_type === 'purchase') {
                        // Purchase type declined: amount goes to debit (bank didn't pay out)
                        $debit = $cheque->amount;
                        $type = 'Declined Cheque - Purchase';
                    } elseif ($cheque->transaction_type === 'opening_balance') {
                        // Opening balance declined: check voucher type
                        if ($cheque->voucher_no && strpos($cheque->voucher_no, 'REC-') === 0) {
                            $debit = $cheque->amount;
                            $type = 'Declined Cheque - Opening Balance (Receive)';
                        } elseif ($cheque->voucher_no && strpos($cheque->voucher_no, 'PAY-') === 0) {
                            $credit = $cheque->amount;
                            $type = 'Declined Cheque - Opening Balance (Payment)';
                        }
                    }
                } else {
                    // For non-bank account ledgers: use existing logic
                    if ($cheque->voucher_no && strpos($cheque->voucher_no, 'REC-') === 0) {
                        $debit = $cheque->amount;
                        $type = 'Declined Cheque - Receive Voucher';
                    } elseif ($cheque->voucher_no && strpos($cheque->voucher_no, 'PAY-') === 0) {
                        $credit = $cheque->amount;
                        $type = 'Declined Cheque - Payment Voucher';
                    } elseif (in_array($cheque->transaction_type, ['sale', 'invoice'])) {
                        $debit = $cheque->amount;
                        $type = 'Declined Cheque - ' . ucfirst($cheque->transaction_type);
                    } elseif ($cheque->transaction_type === 'purchase') {
                        $credit = $cheque->amount;
                        $type = 'Declined Cheque - Purchase';
                    } elseif ($cheque->transaction_type === 'opening_balance') {
                        if ($cheque->voucher_no && strpos($cheque->voucher_no, 'REC-') === 0) {
                            $debit = $cheque->amount;
                            $type = 'Declined Cheque - Opening Balance (Receive)';
                        } elseif ($cheque->voucher_no && strpos($cheque->voucher_no, 'PAY-') === 0) {
                            $credit = $cheque->amount;
                            $type = 'Declined Cheque - Opening Balance (Payment)';
                        }
                    }
                }
            }

            return [
                'date' => $cheque->payment_date ? $cheque->payment_date->format('Y-m-d') : '',
                'type' => $type ?: 'Declined Cheque',
                'description' => $this->getDeclinedChequeDescription($cheque),
                'debit' => $debit,
                'credit' => $credit,
                'balance' => 0,
            ];
        })->filter(function($transaction) {
            // Filter out transactions where both debit and credit are 0
            return $transaction['debit'] > 0 || $transaction['credit'] > 0;
        })->values()->toArray();
    }

    private function getDeclinedChequeDescription($cheque)
    {
        $description = "Declined Cheque";

        // Add voucher number if available
        if ($cheque->voucher_no) {
            $description .= ": {$cheque->voucher_no}";
        }

        // Add reference number if available
        if ($cheque->reference_no) {
            $description .= " (Ref: {$cheque->reference_no})";
        }

        // Add cheque details
        if ($cheque->cheque_no) {
            $description .= " - Cheque No: {$cheque->cheque_no}";
        }

        if ($cheque->bank_name) {
            $description .= " - Bank: {$cheque->bank_name}";
        }

        // Add transaction type context
        if ($cheque->transaction_type) {
            $description .= " ({$cheque->transaction_type})";
        }

        return $description;
    }

    private function getDescription($type, $referenceNumber)
    {
        switch ($type) {
            case 'sales':
            case 'purchase':
                return "Bill Number: {$referenceNumber}";
            case 'invoice':
                return "Invoice Number: {$referenceNumber}";
            default:
                return ucfirst($type) . ": {$referenceNumber}";
        }
    }

    private function getVoucherDescription($type, $voucherNo)
    {
        return "{$type}: {$voucherNo}";
    }

    private function calculateRunningBalance($transactions, $openingBalance, $personType, $person = null)
    {
        $result = [];

        $openingDebit = '';
        $openingCredit = '';
        $balance = 0;

        if ($openingBalance != 0) {
            if ($personType === 'Customer') {
                $openingDebit = abs($openingBalance);
                $balance = $openingBalance;
            } elseif ($personType === 'Supplier') {
                $openingCredit = abs($openingBalance);
                $balance = -$openingBalance;
            } elseif ($personType === 'Other' && $person) {
                $mainAccountType = $this->getMainAccountType($person->account_group);
                $isDebitAccount = $this->isDebitAccountType($mainAccountType);

                if ($isDebitAccount) {
                    $openingDebit = abs($openingBalance);
                    $balance = $openingBalance;
                } else {
                    $openingCredit = abs($openingBalance);
                    $balance = -$openingBalance;
                }
            }
        }

        $result[] = [
            'date' => '',
            'type' => '',
            'description' => 'Opening Balance',
            'debit' => $openingDebit,
            'credit' => $openingCredit,
            'balance' => $balance,
            'is_opening_balance' => true,
        ];

        foreach ($transactions as $transaction) {
            $balance += $transaction['debit'] - $transaction['credit'];
            $transaction['balance'] = $balance;
            $transaction['is_opening_balance'] = false;
            $result[] = $transaction;
        }

        return $result;
    }

    /**
     * Helper: Get approved sales returns for a customer in a date range (credit/cheque only)
     */
    private function getApprovedSalesReturnTransactions($personType, $personId, $fromDate, $toDate)
    {
        if ($personType !== 'Customer') return [];
        // Fetch all approved sales returns for this customer in date range
        $returns = SalesReturn::where('status', 'approved')
            ->where(function($q) use ($personId) {
                $q->whereHas('sale', function($q2) use ($personId) {
                    $q2->where('customer_id', $personId);
                })
                ->orWhereHas('invoice', function($q2) use ($personId) {
                    $q2->where('customer_id', $personId);
                });
            })
            ->whereBetween('created_at', [$fromDate, $toDate])
            ->with(['sale', 'invoice', 'items'])
            ->get();
        $transactions = [];
        foreach ($returns as $return) {
            // Determine if related sale/invoice is credit/cheque
            $isCreditOrCheque = false;
            $reference = '';
            $date = $return->created_at ? $return->created_at->format('Y-m-d') : '';
            if ($return->sale && in_array(strtolower($return->sale->payment_type), ['credit', 'cheque'])) {
                $isCreditOrCheque = true;
                $reference = $return->bill_number;
            } elseif ($return->invoice && in_array(strtolower($return->invoice->payment_method), ['credit', 'cheque'])) {
                $isCreditOrCheque = true;
                $reference = $return->invoice_no;
            }
            if ($isCreditOrCheque) {
                $amount = $return->items->sum(function($item) {
                    return $item->quantity * $item->selling_cost;
                });
                if ($amount > 0) {
                    $transactions[] = [
                        'date' => $date,
                        'type' => 'Sales Return',
                        'description' => 'Sales Return for Bill/Invoice: ' . $reference,
                        'debit' => 0,
                        'credit' => $amount,
                        'balance' => 0,
                    ];
                }
            }
        }
        return $transactions;
    }

    /**
     * Get approved purchase return transactions for suppliers (credit/cheque only)
     */
    private function getApprovedPurchaseReturnTransactions($personType, $personId, $fromDate, $toDate)
    {
        if ($personType !== 'Supplier') {
            return collect([]);
        }

        $transactions = [];
        $purchaseReturns = PurchaseReturn::where('supplier_id', $personId)
            ->where('status', 'approved')
            ->whereBetween('created_at', [$fromDate, $toDate])
            ->get();

        foreach ($purchaseReturns as $purchaseReturn) {
            // Calculate total return amount
            $returnAmount = PurchaseReturnItem::where('purchase_return_id', $purchaseReturn->id)
                ->get()
                ->sum(function($item) {
                    return $item->quantity * $item->buying_cost;
                });

            if ($returnAmount > 0) {
                $transactions[] = [
                    'date' => $purchaseReturn->created_at ? $purchaseReturn->created_at->format('Y-m-d') : '',
                    'type' => 'Purchase Return',
                    'description' => 'Purchase Return: ' . $purchaseReturn->invoice_number,
                    'debit' => $returnAmount, // Debit for supplier (reduces payable)
                    'credit' => 0,
                    'balance' => 0,
                ];
            }
        }
        return $transactions;
    }
}