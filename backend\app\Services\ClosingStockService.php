<?php

namespace App\Services;

use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\PurchaseItem;
use App\Models\InvoiceItem;
use App\Models\SaleItem;
use App\Models\SalesReturnItem;
use App\Models\PurchaseReturnItem;
use App\Models\StockRecheck;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ClosingStockService
{
    /**
     * Calculate and update closing stock for a specific product variant
     */
    public function updateVariantClosingStock(ProductVariant $variant)
    {
        try {
            $closingStock = $this->calculateVariantClosingStock($variant);
            
            $oldClosingStock = $variant->closing_stock_quantity;
            $variant->update(['closing_stock_quantity' => $closingStock]);
            
            Log::info("Updated variant {$variant->product_variant_id} closing stock: {$oldClosingStock} -> {$closingStock}");
            
            return $closingStock;
        } catch (\Exception $e) {
            Log::error("Error updating variant closing stock: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Calculate and update closing stock for a specific product
     */
    public function updateProductClosingStock(Product $product)
    {
        try {
            $closingStock = $this->calculateProductClosingStock($product);
            
            $oldClosingStock = $product->closing_stock_quantity;
            $product->update(['closing_stock_quantity' => $closingStock]);
            
            Log::info("Updated product {$product->product_id} closing stock: {$oldClosingStock} -> {$closingStock}");
            
            return $closingStock;
        } catch (\Exception $e) {
            Log::error("Error updating product closing stock: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Calculate closing stock for a product variant
     */
    public function calculateVariantClosingStock(ProductVariant $variant)
    {
        // Check for latest stock recheck
        $latestRecheck = StockRecheck::where('product_variant_id', $variant->product_variant_id)
            ->where('recheck_type', 'batch')
            ->where('update_actual_stock', true)
            ->orderBy('created_at', 'desc')
            ->first();

        if ($latestRecheck && $latestRecheck->corrected_closing_stock !== null) {
            // Use recheck as base and add subsequent transactions
            $recheckDate = $latestRecheck->created_at;
            $baseClosingStock = $latestRecheck->corrected_closing_stock;

            // Calculate purchases after recheck
            $purchasedAfterRecheck = PurchaseItem::where('product_variant_id', $variant->product_variant_id)
                ->whereHas('purchase', function ($q) use ($recheckDate) {
                    $q->where('created_at', '>=', $recheckDate);
                })
                ->sum('quantity') ?? 0;

            // Calculate sales after recheck
            $soldAfterRecheckInvoice = InvoiceItem::where('product_variant_id', $variant->product_variant_id)
                ->whereHas('invoice', function ($q) use ($recheckDate) {
                    $q->where('created_at', '>=', $recheckDate);
                })
                ->sum(DB::raw('quantity + IFNULL(free,0)')) ?? 0;

            $soldAfterRecheckPOS = SaleItem::where('product_variant_id', $variant->product_variant_id)
                ->whereHas('sale', function ($q) use ($recheckDate) {
                    $q->where('created_at', '>=', $recheckDate);
                })
                ->sum(DB::raw('quantity + IFNULL(free_qty,0)')) ?? 0;

            $soldAfterRecheck = $soldAfterRecheckInvoice + $soldAfterRecheckPOS;

            // Calculate returns after recheck
            $salesReturnedAfterRecheck = SalesReturnItem::where('product_variant_id', $variant->product_variant_id)
                ->whereHas('salesReturn', function ($q) use ($recheckDate) {
                    $q->where('created_at', '>=', $recheckDate)
                      ->where('status', 'approved');
                })
                ->sum('quantity') ?? 0;

            $purchaseReturnedAfterRecheck = PurchaseReturnItem::where('product_variant_id', $variant->product_variant_id)
                ->whereHas('purchaseReturn', function ($q) use ($recheckDate) {
                    $q->where('created_at', '>=', $recheckDate)
                      ->where('status', 'approved');
                })
                ->sum('quantity') ?? 0;

            $closingStock = $baseClosingStock + $purchasedAfterRecheck - $soldAfterRecheck + $salesReturnedAfterRecheck - $purchaseReturnedAfterRecheck;
        } else {
            // Calculate from all transactions
            $openingStock = $variant->opening_stock_quantity ?? 0;

            // Total purchases
            $totalPurchased = PurchaseItem::where('product_variant_id', $variant->product_variant_id)
                ->sum('quantity') ?? 0;

            // Total sales
            $totalSoldInvoice = InvoiceItem::where('product_variant_id', $variant->product_variant_id)
                ->sum(DB::raw('quantity + IFNULL(free,0)')) ?? 0;

            $totalSoldPOS = SaleItem::where('product_variant_id', $variant->product_variant_id)
                ->sum(DB::raw('quantity + IFNULL(free_qty,0)')) ?? 0;

            $totalSold = $totalSoldInvoice + $totalSoldPOS;

            // Total returns
            $totalSalesReturned = SalesReturnItem::where('product_variant_id', $variant->product_variant_id)
                ->whereHas('salesReturn', function ($q) {
                    $q->where('status', 'approved');
                })
                ->sum('quantity') ?? 0;

            $totalPurchaseReturned = PurchaseReturnItem::where('product_variant_id', $variant->product_variant_id)
                ->whereHas('purchaseReturn', function ($q) {
                    $q->where('status', 'approved');
                })
                ->sum('quantity') ?? 0;

            $closingStock = $openingStock + $totalPurchased - $totalSold + $totalSalesReturned - $totalPurchaseReturned;
        }

        return max(0, $closingStock);
    }

    /**
     * Calculate closing stock for a product (sum of all variants)
     */
    public function calculateProductClosingStock(Product $product)
    {
        $variants = $product->variants;
        
        if ($variants->isEmpty()) {
            // For products without variants, calculate directly
            $openingStock = $product->opening_stock_quantity ?? 0;

            // Total purchases
            $totalPurchased = PurchaseItem::where('product_id', $product->product_id)
                ->sum('quantity') ?? 0;

            // Total sales
            $totalSoldInvoice = InvoiceItem::where('product_id', $product->product_id)
                ->sum(DB::raw('quantity + IFNULL(free,0)')) ?? 0;

            $totalSoldPOS = SaleItem::where('product_id', $product->product_id)
                ->sum(DB::raw('quantity + IFNULL(free_qty,0)')) ?? 0;

            $totalSold = $totalSoldInvoice + $totalSoldPOS;

            // Total returns
            $totalSalesReturned = SalesReturnItem::where('product_id', $product->product_id)
                ->whereHas('salesReturn', function ($q) {
                    $q->where('status', 'approved');
                })
                ->sum('quantity') ?? 0;

            $totalPurchaseReturned = PurchaseReturnItem::where('product_id', $product->product_id)
                ->whereHas('purchaseReturn', function ($q) {
                    $q->where('status', 'approved');
                })
                ->sum('quantity') ?? 0;

            return max(0, $openingStock + $totalPurchased - $totalSold + $totalSalesReturned - $totalPurchaseReturned);
        } else {
            // For products with variants, calculate fresh closing stock for each variant and sum them
            $totalClosingStock = 0;
            foreach ($variants as $variant) {
                $totalClosingStock += $this->calculateVariantClosingStock($variant);
            }
            return $totalClosingStock;
        }
    }

    /**
     * Update closing stock for all products and variants
     */
    public function updateAllClosingStock()
    {
        try {
            DB::beginTransaction();

            Log::info('Starting bulk closing stock update...');

            // Update all variants first
            $variants = ProductVariant::all();
            $variantUpdatedCount = 0;

            foreach ($variants as $variant) {
                $oldClosingStock = $variant->closing_stock_quantity;
                $newClosingStock = $this->calculateVariantClosingStock($variant);
                
                if ($oldClosingStock !== $newClosingStock) {
                    $variant->update(['closing_stock_quantity' => $newClosingStock]);
                    $variantUpdatedCount++;
                }
            }

            // Update all products
            $products = Product::all();
            $productUpdatedCount = 0;

            foreach ($products as $product) {
                $oldClosingStock = $product->closing_stock_quantity;
                $newClosingStock = $this->calculateProductClosingStock($product);
                
                if ($oldClosingStock !== $newClosingStock) {
                    $product->update(['closing_stock_quantity' => $newClosingStock]);
                    $productUpdatedCount++;
                }
            }

            DB::commit();

            Log::info("Bulk closing stock update completed. Updated {$variantUpdatedCount} variants and {$productUpdatedCount} products.");

            return [
                'variants_updated' => $variantUpdatedCount,
                'products_updated' => $productUpdatedCount
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error in bulk closing stock update: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update closing stock when a transaction occurs
     */
    public function updateClosingStockOnTransaction($productId, $productVariantId = null)
    {
        try {
            // Update product closing stock first
            $product = Product::find($productId);
            if ($product) {
                $this->updateProductClosingStock($product);
            }

            // Update variant closing stock if variant ID is provided
            if ($productVariantId) {
                $variant = ProductVariant::find($productVariantId);
                if ($variant) {
                    $this->updateVariantClosingStock($variant);
                }
            } else {
                // If no variant ID provided, update all variants for this product
                $variants = ProductVariant::where('product_id', $productId)->get();
                foreach ($variants as $variant) {
                    $this->updateVariantClosingStock($variant);
                }
            }

        } catch (\Exception $e) {
            Log::error('Error updating closing stock on transaction: ' . $e->getMessage());
            // Don't throw - we don't want to break the main transaction
        }
    }
} 